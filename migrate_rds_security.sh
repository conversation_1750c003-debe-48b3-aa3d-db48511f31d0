#!/bin/bash
# RDS SOC2 Compliance Migration Script
# This script helps migrate RDS instances to SOC2 compliant configuration

set -e

echo "=== RDS SOC2 Compliance Migration ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if terraform is available
if ! command -v terraform &> /dev/null; then
    print_error "Terraform is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "modules/eyecue_rds/main.tf" ]; then
    print_error "Please run this script from the terraform root directory"
    exit 1
fi

print_status "Starting RDS SOC2 compliance migration..."

# Phase 1: Plan the changes
print_status "Phase 1: Planning Terraform changes..."
echo ""

print_warning "The following changes will be made:"
echo "1. Security group will be updated to restrict access to AWS service CIDR blocks"
echo "2. RDS instances will be set to NOT publicly accessible (rds_publicly_accessible = false)"
echo "3. Access will be limited to:"
echo "   - AWS services in ap-southeast-2 region"
echo "   - Internal VPC traffic"
echo ""

read -p "Do you want to continue with the migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Migration cancelled by user"
    exit 0
fi

# Phase 2: Backup current state
print_status "Phase 2: Creating backup of current Terraform state..."
BACKUP_DIR="terraform_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup terraform state
if [ -f "terraform.tfstate" ]; then
    cp terraform.tfstate "$BACKUP_DIR/"
    print_status "Terraform state backed up to $BACKUP_DIR/"
fi

# Backup terraform files
cp -r modules/eyecue_rds "$BACKUP_DIR/"
print_status "RDS module backed up to $BACKUP_DIR/"

# Phase 3: Validate the plan
print_status "Phase 3: Validating Terraform configuration..."
terraform init -upgrade
terraform validate

if [ $? -ne 0 ]; then
    print_error "Terraform validation failed. Please fix the configuration."
    exit 1
fi

print_status "Terraform configuration is valid"

# Phase 4: Show the plan
print_status "Phase 4: Showing Terraform plan..."
echo ""
print_warning "Please review the following plan carefully:"
echo ""

terraform plan -var="rds_publicly_accessible=false"

echo ""
read -p "Do you want to apply these changes? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Migration cancelled by user"
    exit 0
fi

# Phase 5: Apply the changes
print_status "Phase 5: Applying Terraform changes..."
terraform apply -var="rds_publicly_accessible=false" -auto-approve

if [ $? -eq 0 ]; then
    print_status "Migration completed successfully!"
    echo ""
    print_status "Summary of changes:"
    echo "✅ RDS instances are no longer publicly accessible"
    echo "✅ Security group restricts access to AWS service CIDR blocks"
    echo "✅ VPC internal access is maintained"
    echo ""
    print_status "Next steps:"
    echo "1. Test connectivity from your applications"
    echo "2. Update any hardcoded connection strings if needed"
    echo "3. Set up VPN or bastion host for administrative access"
    echo "4. Enable VPC Flow Logs for monitoring"
    echo "5. Document the changes for SOC2 audit"
    echo ""
    print_status "Backup files are available in: $BACKUP_DIR"
else
    print_error "Migration failed! Please check the errors above."
    print_warning "You can restore from backup in: $BACKUP_DIR"
    exit 1
fi

# Phase 6: Verification
print_status "Phase 6: Verifying the deployment..."
echo ""

# Check if RDS instances are accessible
print_status "Checking RDS instance status..."
aws rds describe-db-instances --query 'DBInstances[?DBInstanceIdentifier==`master-postgres`].[DBInstanceIdentifier,PubliclyAccessible,VpcSecurityGroups[0].VpcSecurityGroupId]' --output table

print_status "Migration completed! Your RDS instances are now SOC2 compliant."
echo ""
print_warning "Important: Make sure to test your applications to ensure they can still connect to the database."
