#!/usr/bin/env python3
"""
Analyze AWS IP ranges and RDS connections for SOC2 compliance
"""
import json
import ipaddress
import requests
from collections import defaultdict

def fetch_aws_ip_ranges():
    """Fetch AWS IP ranges from official source"""
    url = "https://ip-ranges.amazonaws.com/ip-ranges.json"
    response = requests.get(url)
    return response.json()

def analyze_rds_connections():
    """Analyze the RDS connections from the file"""
    connections = []
    with open('rds_connections.txt', 'r') as f:
        for line in f:
            ip = line.strip().split(':')[0]
            try:
                ipaddress.IPv4Address(ip)
                connections.append(ip)
            except:
                continue
    return connections

def get_aws_ranges_for_region(aws_data, region="ap-southeast-2"):
    """Get AWS IP ranges for specific region"""
    ranges = defaultdict(list)
    
    for prefix in aws_data['prefixes']:
        if prefix['region'] == region:
            ranges[prefix['service']].append(prefix['ip_prefix'])
    
    return ranges

def consolidate_cidr_ranges(ip_list):
    """Consolidate IP addresses into CIDR ranges"""
    networks = []
    for ip in ip_list:
        try:
            # Try to create a /32 network for single IPs
            if '/' not in ip:
                ip = f"{ip}/32"
            networks.append(ipaddress.IPv4Network(ip, strict=False))
        except:
            continue
    
    # Sort networks
    networks.sort()
    
    # Consolidate overlapping networks
    consolidated = []
    for network in networks:
        if not consolidated:
            consolidated.append(network)
            continue
            
        # Check if this network can be merged with the last one
        last = consolidated[-1]
        try:
            # If networks are adjacent or overlapping, try to merge
            if network.subnet_of(last) or last.subnet_of(network):
                # Keep the larger network
                if network.supernet_of(last):
                    consolidated[-1] = network
                # else keep the existing one
            elif network.overlaps(last):
                # Create a supernet that contains both
                supernet = last.supernet()
                while not (network.subnet_of(supernet) and last.subnet_of(supernet)):
                    supernet = supernet.supernet()
                consolidated[-1] = supernet
            else:
                consolidated.append(network)
        except:
            consolidated.append(network)
    
    return consolidated

def main():
    print("=== AWS RDS Security Analysis for SOC2 Compliance ===\n")
    
    # Fetch AWS IP ranges
    print("Fetching AWS IP ranges...")
    aws_data = fetch_aws_ip_ranges()
    
    # Analyze RDS connections
    print("Analyzing RDS connections...")
    rds_ips = analyze_rds_connections()
    print(f"Found {len(rds_ips)} unique IP addresses in connections\n")
    
    # Get AWS ranges for ap-southeast-2
    ap_southeast_2_ranges = get_aws_ranges_for_region(aws_data, "ap-southeast-2")
    
    print("=== AWS Services in ap-southeast-2 ===")
    for service, ranges in ap_southeast_2_ranges.items():
        print(f"{service}: {len(ranges)} ranges")
        for r in ranges[:3]:  # Show first 3 ranges
            print(f"  {r}")
        if len(ranges) > 3:
            print(f"  ... and {len(ranges) - 3} more")
        print()
    
    # Consolidate RDS connection IPs into CIDR ranges
    print("=== Consolidating RDS Connection IPs ===")
    consolidated_ranges = consolidate_cidr_ranges(rds_ips)
    
    print(f"Original IPs: {len(rds_ips)}")
    print(f"Consolidated to: {len(consolidated_ranges)} CIDR ranges\n")
    
    print("Top 20 consolidated ranges:")
    for i, network in enumerate(consolidated_ranges[:20]):
        print(f"  {network}")
    
    if len(consolidated_ranges) > 20:
        print(f"  ... and {len(consolidated_ranges) - 20} more ranges")
    
    # Generate recommendations
    print("\n=== RECOMMENDATIONS FOR SOC2 COMPLIANCE ===")
    
    # Get all AWS ranges for ap-southeast-2
    all_aws_ranges = []
    for ranges in ap_southeast_2_ranges.values():
        all_aws_ranges.extend(ranges)
    
    print(f"\n1. AWS ap-southeast-2 Service Ranges ({len(all_aws_ranges)} total):")
    print("   Key ranges to allow for AWS services:")
    
    # Show major AWS service blocks
    major_blocks = [
        "**********/15",  # EC2 ap-southeast-2
        "**********/14",  # EC2 ap-southeast-2  
        "**********/16",  # EC2 ap-southeast-2
        "*********/15",   # EC2 ap-southeast-2
        "*********/17",   # EC2 ap-southeast-2
        "3.104.0.0/14",   # EC2 ap-southeast-2
        "3.25.0.0/16",    # EC2 ap-southeast-2
        "3.26.0.0/16",    # EC2 ap-southeast-2
    ]
    
    for block in major_blocks:
        print(f"   - {block}")
    
    print(f"\n2. Cross-Region AWS Access:")
    print("   For multi-region deployments, consider these additional ranges:")
    
    # Get some other regions that might be relevant
    other_regions = ["us-east-1", "us-west-2", "eu-west-1"]
    for region in other_regions:
        region_ranges = get_aws_ranges_for_region(aws_data, region)
        ec2_ranges = region_ranges.get('EC2', [])
        if ec2_ranges:
            print(f"   {region}: {ec2_ranges[0]} (and {len(ec2_ranges)-1} more)")

if __name__ == "__main__":
    main()
