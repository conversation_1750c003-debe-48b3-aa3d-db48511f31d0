# RDS SOC2 Compliance Security Analysis

## Current Security Issues

### Critical Findings
1. **Public Access Enabled**: Both master and replica RDS instances have `publicly_accessible = true`
2. **Open Security Group**: Security group allows access from `0.0.0.0/0` (entire internet)
3. **SOC2 Non-Compliance**: Current configuration violates SOC2 requirements for data protection

### Current Configuration Problems
```hcl
# In modules/eyecue_rds/main.tf
publicly_accessible = true  # Lines 33, 81
cidr_blocks = ["0.0.0.0/0"] # Line 177
```

## Analysis of RDS Connections

### Connection Summary
- **Total IPs analyzed**: 1,962 unique IP addresses
- **Primary regions**: ap-southeast-2 (bkg-nzl, mcd-aus)
- **IP ranges identified**: All connections are from AWS infrastructure

### Major AWS Service Blocks (ap-southeast-2)
The connections come from these AWS service ranges:
- `**********/15` - EC2 instances
- `**********/14` - EC2 instances  
- `**********/16` - EC2 instances
- `*********/14` - EC2 instances
- `********/16` - EC2 instances
- `********/16` - EC2 instances
- `********/16` - EC2 instances
- `**********/16` - EC2 instances
- `*********/15` - EC2 instances
- `**********/16` - Lambda/Fargate services

## SOC2 Compliant Solution

### 1. Disable Public Access
```hcl
# Set publicly_accessible to false
publicly_accessible = false
```

### 2. Restrict Security Group Access
Replace the current open security group with AWS service-specific CIDR blocks:

```hcl
# Primary ap-southeast-2 AWS service ranges
variable "aws_service_cidrs_ap_southeast_2" {
  description = "AWS service CIDR blocks for ap-southeast-2"
  type        = list(string)
  default = [
    "**********/15",   # EC2 ap-southeast-2
    "**********/14",   # EC2 ap-southeast-2
    "**********/16",   # EC2 ap-southeast-2
    "**********/16",   # EC2 ap-southeast-2
    "*********/14",    # EC2 ap-southeast-2
    "********/16",     # EC2 ap-southeast-2
    "********/16",     # EC2 ap-southeast-2
    "********/16",     # EC2 ap-southeast-2
    "**********/16",   # EC2 ap-southeast-2
    "*********/15",    # EC2 ap-southeast-2
    "*********/17",    # EC2 ap-southeast-2
    "*********/16",    # EC2 ap-southeast-2
    "**********/16",   # Lambda/Fargate
  ]
}

# Cross-region access (if needed)
variable "aws_service_cidrs_cross_region" {
  description = "AWS service CIDR blocks for cross-region access"
  type        = list(string)
  default = [
    # Add specific ranges only if cross-region access is required
    # "**********/16",  # Default VPC range
  ]
}
```

### 3. Updated Security Group Configuration
```hcl
resource "aws_security_group" "main_sec_group" {
  vpc_id      = var.vpc_id
  name        = var.vpc_security_group_name
  description = var.vpc_security_group_description
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow access from AWS services in ap-southeast-2
  dynamic "ingress" {
    for_each = var.aws_service_cidrs_ap_southeast_2
    content {
      from_port   = 5432
      to_port     = 5432
      protocol    = "tcp"
      cidr_blocks = [ingress.value]
      description = "PostgreSQL access from AWS services"
    }
  }

  # Allow access from VPC CIDR (internal services)
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr_block]
    description = "PostgreSQL access from VPC"
  }

  tags = var.tags
}
```

### 4. Network Architecture Improvements

#### Option A: Private Subnets (Recommended)
- Move RDS instances to private subnets
- Use NAT Gateway for outbound internet access
- Access via VPN or bastion host

#### Option B: Public Subnets with Restricted Access
- Keep in public subnets but restrict security group
- Use the AWS service CIDR blocks above
- Monitor access logs regularly

## Implementation Steps

### Phase 1: Immediate Security Hardening
1. Update security group to use AWS service CIDR blocks
2. Keep `publicly_accessible = true` temporarily for testing
3. Verify all services can still connect

### Phase 2: Full SOC2 Compliance
1. Set `publicly_accessible = false`
2. Ensure all applications connect through private networking
3. Implement proper VPN/bastion access for administration

### Phase 3: Monitoring and Compliance
1. Enable VPC Flow Logs
2. Set up CloudTrail for RDS API calls
3. Implement regular security group audits
4. Document access patterns for SOC2 audits

## Multi-Region Considerations

For deployments across multiple AWS regions, add these ranges as needed:

### US East 1 (us-east-1)
```hcl
"*********/15",     # EC2 us-east-1
"********/14",      # EC2 us-east-1
"**********/14",    # EC2 us-east-1
```

### US West 2 (us-west-2)  
```hcl
"**********/15",    # EC2 us-west-2
"*********/14",     # EC2 us-west-2
```

### Europe West 1 (eu-west-1)
```hcl
"*********/15",     # EC2 eu-west-1
"**********/16",    # EC2 eu-west-1
```

## Compliance Benefits

This solution provides:
- ✅ **SOC2 Compliance**: Restricts database access to authorized AWS services only
- ✅ **Defense in Depth**: Multiple layers of security controls
- ✅ **Audit Trail**: Clear documentation of allowed access patterns
- ✅ **Scalability**: Works across multiple regions and accounts
- ✅ **Maintainability**: Uses official AWS IP ranges that are regularly updated

## Next Steps

1. Review and approve the security group changes
2. Test connectivity from all required services
3. Implement the changes in a staging environment first
4. Plan the migration to private subnets for full compliance
5. Set up monitoring and alerting for unauthorized access attempts
