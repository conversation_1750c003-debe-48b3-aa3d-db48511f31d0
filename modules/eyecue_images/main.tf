module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}
module "s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.0"
  bucket = "eyecue-${var.client_name}-${var.country}-images"
  #acl                     = "private"
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  versioning = {
    enabled = var.enable_versioning
  }
  cors_rule = [{
    allowed_headers = ["*"]
    allowed_methods = var.allowed_methods
    allowed_origins = ["*"]
    expose_headers  = []
    max_age_seconds = 0
  }]
  tags = merge(var.tags, { "Name" = "eyecue-${var.client_name}-${var.country}-images" })
}
module "tessera_s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.0"
  bucket = "eyecue-${var.client_name}-${var.country}-tessera"
  #acl                     = "private"
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  versioning = {
    enabled = var.enable_versioning
  }
  cors_rule = [{
    allowed_headers = ["*"]
    allowed_methods = var.allowed_methods
    allowed_origins = ["*"]
    expose_headers  = []
    max_age_seconds = 0
  }]
  lifecycle_rule = [
    {
      id     = "ExpireDocuments"
      status = "Enabled"
      filter = {
        prefix = "" # Apply to all objects
      }
      expiration = {
        days = 7
      }
    }
  ]

  tags = merge(var.tags, { "Name" = "eyecue-${var.client_name}-${var.country}-tessera" })
}
data "aws_iam_policy_document" "eyecue_image_sync_policy" {
  statement {
    sid = "EyecueImageSyncIAMPolicy"
    actions = [
      "s3:GetObject",
      "s3:GetObjectAcl",
      "s3:ListBucket",
      "s3:PutObject",
      "s3:PutObjectAcl"
    ]
    resources = [
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-camera-images",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-camera-images/*",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-images",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-images/*",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-tessera",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-tessera/*"
    ]
  }
  statement {
    sid = "EyecueCameraDisplacementSendMessagePolicy"
    actions = [
      "sqs:GetQueueUrl",
      "sqs:SendMessage",
      "sqs:GetQueueAttributes"
    ]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.aws_account_id}:eyecue-camera-displacement-sqs-${var.client_name}-${var.country_full}"
    ]
  }
  statement {
    sid = "EyecueCameraDisplacementKMS"
    actions = [
      "kms:GenerateDataKey"
    ]
    resources = [
      var.kms_arn
    ]
  }
}
resource "aws_iam_policy" "eyecue_image_sync_policy" {
  name        = "EyecueImageSyncPolicy"
  depends_on  = [module.iam_user]
  path        = "/"
  description = ""
  policy      = data.aws_iam_policy_document.eyecue_image_sync_policy.json
}
resource "aws_iam_user_policy_attachment" "eyecue_images_s3_policy_attachment" {
  user       = var.aws_iam_user
  policy_arn = aws_iam_policy.eyecue_image_sync_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
