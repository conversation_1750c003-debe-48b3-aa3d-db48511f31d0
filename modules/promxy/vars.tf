variable "default_tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
    Stack       = "Monitoring"
    Application = "Promxy"
  }
}

variable "promxy_image" {
  type    = string
  default = "quay.io/jacksontj/promxy:v0.0.92"
}

variable "service_name" {
  type    = string
  default = "PromxyService"
}

variable "task_cpu" {
  type    = number
  default = 256
}

variable "task_memory" {
  type    = number
  default = 512
}

variable "desired_count" {
  type    = number
  default = 1
}

variable "vpc_id" {
  type = string
  # e.g., "vpc-0123456789abcdef0"
}

variable "subnets" {
  type = list(string)
  # e.g., ["subnet-11111111", "subnet-22222222"]
}

variable "ecs_cluster_id" {
  type = string
  # Provide the existing ECS cluster name or ARN
}

variable "aws_region" {
  type    = string
  default = "ap-southeast-2"
}

variable "grafana_security_groups" {
  type = list(string)
}

variable "namespace_id" {
  type        = string
  description = "The ID of the namespace to use for service discovery"
}
