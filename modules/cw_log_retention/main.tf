locals {
  module_dir  = abspath(path.module)
  zip_file    = "${local.module_dir}/bin/lambda.zip"
  lambda_srcs = fileset(local.module_dir, "lambda/**")
  tags = merge(var.default_tags, var.tags, { Squad = "Platform" })
}

##############################################################################
# IAM ROLE + POLICY for Lambda
##############################################################################
resource "aws_iam_role" "lambda" {
  name = "cw-log-retention-${data.aws_region.current.name}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect    = "Allow"
      Principal = { Service = "lambda.amazonaws.com" }
      Action    = "sts:AssumeRole"
    }]
  })
  tags = local.tags
}

resource "aws_iam_role_policy_attachment" "basic_logs" {
  role       = aws_iam_role.lambda.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_policy" "lambda_log_permissions" {
  name        = "cw-log-retention-permissions-${data.aws_region.current.name}"
  description = "Allows Lambda to describe log groups and set retention policies."
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:DescribeLogGroups",
          "logs:PutRetentionPolicy"
        ]
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.this.account_id}:log-group:*"
      },
    ]
  })
  tags = local.tags
}

resource "aws_iam_role_policy_attachment" "lambda_log_permissions" {
  role       = aws_iam_role.lambda.name
  policy_arn = aws_iam_policy.lambda_log_permissions.arn
}

##############################################################################
# Lambda Function to check and enforce log retention
# This Lambda function will be triggered by CloudWatch Events
# when a new log group is created or on a scheduled basis.
##############################################################################

# ------ builder --------

resource "null_resource" "lambda_build" {
  triggers = {
    src_hash = sha1(join("", [
      for f in local.lambda_srcs :
      filesha256("${local.module_dir}/${f}")
    ]))
  }

  provisioner "local-exec" {
    command = <<-EOF
      set -euo pipefail
      tmp=$(mktemp -d)

      # copy code
      cp ${local.module_dir}/lambda/*.py "$tmp/"

      # vendor deps
      pip install --quiet \
          -r ${local.module_dir}/lambda/requirements.txt \
          -t "$tmp"

      # zip from inside $tmp *to an absolute file name*
      (cd "$tmp"; zip -qr "${local.zip_file}" .)
    EOF
  }
}

# ------ lambda function --------

resource "aws_lambda_function" "this" {
  function_name = "cw-log-retention-${data.aws_region.current.name}"
  description   = "Automatically enforces ≥ ${var.retention_days}-day retention on all log groups."

  role        = aws_iam_role.lambda.arn
  runtime     = "python3.13"
  handler     = "main.handler"
  memory_size = 128
  timeout     = 300

  filename = local.zip_file
  source_code_hash = base64encode(null_resource.lambda_build.triggers.src_hash)

  # Use a KMS key to encrypt the Lambda environment variables.
  kms_key_arn = aws_kms_key.lambda_env.arn

  environment {
    variables = {
      RETENTION_DAYS = tostring(var.retention_days)
    }
  }

  # Ensure Lambda log group retains the same # of days but allow teams to manage drift.
  lifecycle {
    ignore_changes = [
      # Permit manual overrides without constant Terraform churn.
      environment[0].variables["RETENTION_DAYS"]
    ]
  }

  tags       = local.tags
  depends_on = [null_resource.lambda_build]
}

##############################################################################
# EventBridge RULES – two paths to invocation
# 1. Real‑time: Trigger on *CreateLogGroup* via CloudTrail.
# 2. Scheduled: Re‑scan once every X hours for built‑in drift remediation.
##############################################################################

resource "aws_cloudwatch_event_rule" "create_log_group" {
  name        = "cw-log-retention-create-${data.aws_region.current.name}"
  description = "Invoke Lambda immediately when a new log group is created."
  event_pattern = jsonencode({
    "detail-type" : ["AWS API Call via CloudTrail"],
    "source" : ["aws.logs"],
    "detail" : {
      "eventName" : ["CreateLogGroup"]
    }
  })
  tags = local.tags
}

# Scheduled rule e.g. "rate(6 hours)" to ensure all log groups have the correct retention.
resource "aws_cloudwatch_event_rule" "scheduled" {
  name                = "cw-log-retention-schedule-${data.aws_region.current.name}"
  description         = "Runs every 6 hours to enforce retention drift-free."
  schedule_expression = var.check_schedule
  tags                = local.tags
}

resource "aws_cloudwatch_event_target" "create_target" {
  rule      = aws_cloudwatch_event_rule.create_log_group.name
  target_id = "lambda"
  arn       = aws_lambda_function.this.arn
}

resource "aws_cloudwatch_event_target" "scheduled_target" {
  rule      = aws_cloudwatch_event_rule.scheduled.name
  target_id = "lambda"
  arn       = aws_lambda_function.this.arn
}

resource "aws_lambda_permission" "allow_events_create" {
  statement_id  = "AllowExecutionFromEventsCreate"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.this.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.create_log_group.arn
}

resource "aws_lambda_permission" "allow_events_schedule" {
  statement_id  = "AllowExecutionFromEventsSchedule"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.this.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.scheduled.arn
}

resource "aws_cloudwatch_log_group" "lambda" {
  name              = "/aws/lambda/${aws_lambda_function.this.function_name}"
  retention_in_days = var.retention_days
  kms_key_id = aws_kms_alias.cw_logs.arn # Use a KMS key to encrypt the CloudWatch Logs.
  lifecycle { # allow overwrite by cw_log_retention module
    ignore_changes = [retention_in_days]
  }
  tags = local.tags
}
