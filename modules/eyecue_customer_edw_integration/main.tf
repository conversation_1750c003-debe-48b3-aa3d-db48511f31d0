module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

## Access to this bucket will be defined by Serverless framework

# https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
module "s3_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.0"

  bucket                  = "eyecue-${var.client_name}-${var.country}-${var.bucket_name_reference}"
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  versioning = {
    enabled = var.enable_versioning
  }

  tags = merge(var.tags, { "Name" = "eyecue-${var.client_name}-${var.country}-${var.bucket_name_reference}" })

  lifecycle_rule = [
    {
      id      = "delete_archived"
      enabled = true
      filter = {
        prefix = "archive/"
      }
      expiration = {
        days = 7
      }
      noncurrent_version_expiration = {
        days = 7
      }
      abort_incomplete_multipart_upload_days = 1
    }
  ]
}

data "aws_iam_policy_document" "eyecue_edw_policy_document" {
  statement {
    sid = "EyecueEdwPolicyDocument"
    actions = [
      "s3:Get*",
      "s3:Put*",
      "s3:List*",
      "S3:Delete*"
    ]
    resources = [
      module.s3_bucket.s3_bucket_arn,
      "${module.s3_bucket.s3_bucket_arn}/*"
    ]
  }
}

resource "aws_iam_policy" "eyecue_edw_policy" {
  name        = "EyecueEdwPolicy"
  depends_on  = [module.iam_user]
  path        = "/"
  description = ""

  policy = data.aws_iam_policy_document.eyecue_edw_policy_document.json
}

resource "aws_iam_user_policy_attachment" "eyecue_edw_attachment" {
  user       = module.iam_user.this_iam_user_name
  policy_arn = aws_iam_policy.eyecue_edw_policy.arn
}


resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}

resource "aws_s3_bucket_policy" "allow_customer_access" {
  count  = var.customer_aws_account_id != "" ? 1 : 0
  bucket = module.s3_bucket.s3_bucket_id
  policy = data.aws_iam_policy_document.allow_customer_access.json
}


data "aws_iam_policy_document" "allow_customer_access" {
  statement {
    principals {
      type        = "AWS"
      identifiers = [var.customer_aws_account_id]
    }

    actions = [
      "s3:GetObject",
      "s3:ListBucket",
    ]

    resources = [
      module.s3_bucket.s3_bucket_arn,
      "${module.s3_bucket.s3_bucket_arn}/*",
    ]
  }
}
