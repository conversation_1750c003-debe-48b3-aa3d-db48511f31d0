### https://github.com/terraform-aws-modules/terraform-aws-rds/releases

##############################
# Data Sources
##############################

data "aws_caller_identity" "current" {}

# Get AWS IP ranges for SOC2 compliant security groups
data "aws_ip_ranges" "aws_services" {
  count    = var.enable_soc2_compliance ? 1 : 0
  regions  = var.soc2_allowed_regions
  services = ["ec2", "lambda", "cloudfront", "api_gateway"]
}

locals {
  # Define region-specific AWS service CIDR ranges for SOC2 compliance
  # These are optimized ranges based on actual usage patterns across all deployments
  aws_service_cidrs = var.enable_soc2_compliance ? {
    # ap-southeast-2 (Sydney) - for AUS/NZL customers
    "ap-southeast-2" = [
      "**********/15",   # EC2 ap-southeast-2 (covers 13.210.x.x and 13.211.x.x)
      "**********/14",   # EC2 ap-southeast-2 (covers 13.236.x.x - 13.239.x.x)
      "*********/15",    # EC2 ap-southeast-2 (covers 52.62.x.x and 52.63.x.x)
      "*********/13",    # EC2 ap-southeast-2 (covers 52.64.x.x - 52.71.x.x)
      "*********/16",    # EC2 ap-southeast-2
      "*********/16",    # EC2 ap-southeast-2
      "**********/16",   # EC2 ap-southeast-2
      "**********/15",   # EC2 ap-southeast-2 (covers 54.252.x.x and 54.253.x.x)
      "3.104.0.0/14",    # Lambda/API Gateway ap-southeast-2 (covers 3.104.x.x - 3.107.x.x)
      "3.25.0.0/16",     # Lambda/API Gateway ap-southeast-2
      "3.26.0.0/15",     # Lambda/API Gateway ap-southeast-2 (covers 3.26.x.x and 3.27.x.x)
    ]

    # us-east-1 (N. Virginia) - for USA customers
    "us-east-1" = [
      "3.208.0.0/12",    # EC2 us-east-1 (covers 3.208.x.x - 3.223.x.x)
      "3.224.0.0/12",    # EC2 us-east-1 (covers 3.224.x.x - 3.239.x.x)
      "18.204.0.0/14",   # EC2 us-east-1 (covers 18.204.x.x - 18.207.x.x)
      "18.208.0.0/13",   # EC2 us-east-1 (covers 18.208.x.x - 18.215.x.x)
      "34.192.0.0/12",   # EC2 us-east-1 (covers 34.192.x.x - 34.207.x.x)
      "34.224.0.0/12",   # EC2 us-east-1 (covers 34.224.x.x - 34.239.x.x)
      "52.0.0.0/11",     # EC2 us-east-1 (covers 52.0.x.x - 52.31.x.x)
      "52.44.0.0/14",    # EC2 us-east-1 (covers 52.44.x.x - 52.47.x.x)
      "52.86.0.0/15",    # EC2 us-east-1 (covers 52.86.x.x and 52.87.x.x)
      "54.144.0.0/14",   # EC2 us-east-1 (covers 54.144.x.x - 54.147.x.x)
      "54.156.0.0/14",   # EC2 us-east-1 (covers 54.156.x.x - 54.159.x.x)
      "54.196.0.0/15",   # EC2 us-east-1 (covers 54.196.x.x and 54.197.x.x)
      "54.204.0.0/15",   # EC2 us-east-1 (covers 54.204.x.x and 54.205.x.x)
      "54.208.0.0/15",   # EC2 us-east-1 (covers 54.208.x.x and 54.209.x.x)
      "54.224.0.0/15",   # EC2 us-east-1 (covers 54.224.x.x and 54.225.x.x)
      "54.234.0.0/15",   # EC2 us-east-1 (covers 54.234.x.x and 54.235.x.x)
    ]

    # us-west-1 (N. California) - for USA customers
    "us-west-1" = [
      "13.52.0.0/14",    # EC2 us-west-1 (covers 13.52.x.x - 13.55.x.x)
      "13.56.0.0/14",    # EC2 us-west-1 (covers 13.56.x.x - 13.59.x.x)
      "54.151.0.0/16",   # EC2 us-west-1
      "54.153.0.0/16",   # EC2 us-west-1
      "54.176.0.0/15",   # EC2 us-west-1 (covers 54.176.x.x and 54.177.x.x)
      "54.183.0.0/16",   # EC2 us-west-1
      "54.193.0.0/16",   # EC2 us-west-1
      "184.72.0.0/15",   # EC2 us-west-1 (covers 184.72.x.x and 184.73.x.x)
    ]

    # ca-central-1 (Canada) - for CAN customers
    "ca-central-1" = [
      "15.222.0.0/15",   # EC2 ca-central-1 (covers 15.222.x.x and 15.223.x.x)
      "35.182.0.0/15",   # EC2 ca-central-1 (covers 35.182.x.x and 35.183.x.x)
      "52.60.0.0/14",    # EC2 ca-central-1 (covers 52.60.x.x - 52.63.x.x)
      "99.79.0.0/16",    # EC2 ca-central-1
    ]

    # CloudFront Global ranges (used by all regions)
    "global" = [
      "16.176.0.0/15",   # CloudFront Global (covers 16.176.x.x and 16.177.x.x)
      "13.32.0.0/15",    # CloudFront Global (covers 13.32.x.x and 13.33.x.x)
      "13.224.0.0/14",   # CloudFront Global (covers 13.224.x.x - 13.227.x.x)
      "54.230.0.0/15",   # CloudFront Global (covers 54.230.x.x and 54.231.x.x)
      "54.239.128.0/18", # CloudFront Global
      "99.84.0.0/16",    # CloudFront Global
      "204.246.164.0/22", # CloudFront Global
      "204.246.168.0/22", # CloudFront Global
      "204.246.174.0/23", # CloudFront Global
      "204.246.176.0/20", # CloudFront Global
    ]
  } : {}

  # Flatten the CIDR ranges for the allowed regions
  allowed_cidrs = var.enable_soc2_compliance ? flatten([
    for region in var.soc2_allowed_regions :
    lookup(local.aws_service_cidrs, region, [])
  ]) : []

  # Add global CloudFront ranges if SOC2 compliance is enabled
  all_allowed_cidrs = var.enable_soc2_compliance ? concat(
    local.allowed_cidrs,
    lookup(local.aws_service_cidrs, "global", [])
  ) : []
}

resource "random_string" "master_password" {
  count   = var.create_random_password ? 1 : 0
  length  = 32
  special = var.special_password
}

module "master" {
  # https://github.com/terraform-aws-modules/terraform-aws-rds
  source  = "terraform-aws-modules/rds/aws"
  version = "5.2.0"

  identifier     = var.rds_instance_identifier
  engine         = var.rds_engine
  engine_version = var.rds_engine_version

  instance_class        = var.rds_master_instance_class
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_type          = var.rds_storage_type
  storage_encrypted     = var.rds_storage_encrypted
  iops                  = var.rds_master_iops

  db_name                = var.rds_name
  username               = var.rds_username
  password               = var.create_random_password ? random_string.master_password[0].result : null
  port                   = var.rds_port
  create_random_password = var.rds_create_random_password

  publicly_accessible    = var.rds_publicly_accessible
  vpc_security_group_ids = concat(var.vpc_security_group_ids, [aws_security_group.main_sec_group.id])
  subnet_ids             = var.subnet_ids

  maintenance_window      = var.rds_maintenance_window
  backup_window           = var.rds_backup_window
  backup_retention_period = var.rds_backup_retention_period
  skip_final_snapshot     = var.rds_skip_final_snapshot
  deletion_protection     = var.deletion_protection
  apply_immediately       = var.rds_apply_changes_immediately

  create_db_option_group          = false
  create_db_subnet_group          = var.rds_create_db_subnet_group
  db_subnet_group_name            = var.db_subnet_group_name
  db_subnet_group_use_name_prefix = var.db_subnet_group_use_name_prefix
  timeouts                        = var.rds_timeout

  create_db_parameter_group       = var.create_db_parameter_group
  parameter_group_description     = var.parameter_group_description
  parameter_group_name            = var.parameter_group_name
  parameter_group_use_name_prefix = var.parameter_group_use_name_prefix
  family                          = var.parameter_group_family
  parameters                      = var.parameter_group_parameters

  performance_insights_enabled        = var.rds_performance_insights_enabled
  allow_major_version_upgrade         = var.allow_major_version_upgrade
  iam_database_authentication_enabled = true

  ca_cert_identifier = var.rds_ca_cert_identifier
  tags               = var.rds_master_tags
}

module "replica" {
  create_db_instance = var.create_replica
  source             = "terraform-aws-modules/rds/aws"
  version            = "5.2.0"
  identifier         = "${var.rds_instance_identifier}-replica"

  ### Source database. For cross-region use this_db_instance_arn
  replicate_source_db = "master-postgres"
  # replicate_source_db = module.master.db_instance_id

  engine                = var.rds_engine
  engine_version        = var.rds_engine_version
  instance_class        = var.rds_replica_instance_class
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_encrypted     = true
  publicly_accessible   = var.rds_publicly_accessible
  storage_type          = var.rds_storage_type
  iops                  = var.rds_replica_iops
  skip_final_snapshot   = true
  ### Username and password must not be set for replicas
  username = ""
  password = ""
  port     = var.rds_port

  iam_database_authentication_enabled = true

  vpc_security_group_ids = concat(var.vpc_security_group_ids, [aws_security_group.main_sec_group.id])
  maintenance_window     = "Tue:00:00-Tue:03:00"
  backup_window          = "03:00-06:00"
  apply_immediately      = var.rds_apply_changes_immediately
  ### disable backups to create DB faster

  backup_retention_period = 0
  ### Not allowed to specify a subnet group for replicas in the same region
  create_db_subnet_group    = false
  create_db_option_group    = false
  create_db_parameter_group = false

  ca_cert_identifier = var.rds_ca_cert_identifier
  tags               = var.rds_replica_tags
}

resource "aws_iam_role" "eyecue_admin_role" {
  name = "EyecueRdsAdmin"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Condition = {
        StringLike = {
          "api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc:sub" = "*"
        }
      },
      Action = "sts:AssumeRoleWithWebIdentity",
      Principal = {
        Federated = "arn:aws:iam::${var.aws_account_id}:oidc-provider/api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc"
      }
    }]
  })
  tags = var.tags
}

data "aws_iam_policy_document" "eyecue_admin_policy" {
  statement {
    actions = [
      "rds-db:connect",
    ]
    resources = ["arn:aws:rds-db:${var.aws_region}:${var.aws_account_id}:dbuser:*/eyecue_admin"]
  }
}

resource "aws_iam_policy" "eyecue_admin_policy" {
  name        = "EyecueRdsAdminPermission"
  description = "Custom policy for EyecueAdmin to manage RDS instances"
  policy      = data.aws_iam_policy_document.eyecue_admin_policy.json
  tags        = var.tags
}

resource "aws_iam_role_policy_attachment" "eyecue_admin_policy_attachment" {
  role       = aws_iam_role.eyecue_admin_role.name
  policy_arn = aws_iam_policy.eyecue_admin_policy.arn
}

### Create IAM Policy and Role for IAM authentication

module "rds_access_role" {
  roles_allowed_to_assume = var.eyecue_rds_roles_allowed_to_read
  source                  = "../rds_access_role"
  aws_account_id          = var.aws_account_id
  aws_region              = var.aws_region
  instance_id             = var.create_replica ? module.replica.db_instance_resource_id : module.master.db_instance_resource_id
}


# Security Groups
resource "aws_security_group" "main_sec_group" {
  vpc_id      = var.vpc_id
  name        = var.vpc_security_group_name
  description = var.vpc_security_group_description

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Legacy permissive rule (only when SOC2 compliance is disabled)
  dynamic "ingress" {
    for_each = var.enable_soc2_compliance ? [] : [1]
    content {
      from_port   = 5432
      to_port     = 5432
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
      description = "PostgreSQL access from anywhere (NON-SOC2 COMPLIANT)"
    }
  }

  # SOC2 Compliant ingress rules - Region-aware AWS service ranges
  dynamic "ingress" {
    for_each = var.enable_soc2_compliance ? local.all_allowed_cidrs : []
    content {
      from_port   = 5432
      to_port     = 5432
      protocol    = "tcp"
      cidr_blocks = [ingress.value]
      description = "PostgreSQL access from AWS services (SOC2 compliant)"
    }
  }

  tags = merge(var.tags, {
    Name       = var.enable_soc2_compliance ? "${var.vpc_security_group_name}-soc2-compliant" : var.vpc_security_group_name
    Compliance = var.enable_soc2_compliance ? "SOC2" : "Legacy"
  })
}


module "master_dns_record" {
  source                  = "../cloudflare"
  cloudflare_zone_id      = "13bbaa28a85416bdd354f6014cdac2e3"
  cloudflare_record_name  = "${var.eyecue_rds_stage_name}.master.rds.${var.aws_region}.${var.eyecue_rds_customer_id}.${var.product}"
  cloudflare_record_value = module.master.db_instance_address
  cloudflare_api_key      = var.eyecue_rds_cloudflare_api_key
  cloudflare_record_type  = "CNAME"
}

module "replica_dns_record" {
  source                  = "../cloudflare"
  cloudflare_zone_id      = "13bbaa28a85416bdd354f6014cdac2e3"
  cloudflare_record_name  = "${var.eyecue_rds_stage_name}.replica.rds.${var.aws_region}.${var.eyecue_rds_customer_id}.${var.product}"
  cloudflare_record_value = module.replica.db_instance_address != "" ? module.replica.db_instance_address : var.eyecue_rds_null_replica_address
  cloudflare_api_key      = var.eyecue_rds_cloudflare_api_key
  cloudflare_record_type  = "CNAME"
}
