# This adds ALL CIDR ranges listed from AWS into the RDS's Security Group's Ingress
# I recognize this solution is crude as it allows communication from all services including those
# outside of our VPC. However this is temporarily done for SOC2 compliance as these instances will
# be removed soon.
data "http" "aws_ip_ranges" {
  count = var.use_all_aws_ip_ranges ? 1 : 0
  url   = "https://ip-ranges.amazonaws.com/ip-ranges.json"
}

locals {
  # Parse AWS IP ranges JSON and extract all CIDR blocks
  aws_ip_data = var.use_all_aws_ip_ranges ? jsondecode(data.http.aws_ip_ranges[0].response_body) : null

  # Get all AWS CIDR blocks (EC2, S3, CloudFront, etc.)
  all_aws_cidrs = var.use_all_aws_ip_ranges ? [
    for prefix in local.aws_ip_data.prefixes : prefix.ip_prefix
  ] : []

  # Final CIDR list to use
  final_aws_cidrs = var.use_all_aws_ip_ranges ? local.all_aws_cidrs : var.aws_service_cidrs_custom
}
