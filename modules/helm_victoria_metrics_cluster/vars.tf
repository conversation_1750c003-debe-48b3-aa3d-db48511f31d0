variable "release_name" {
  description = "The release name for the VictoriaMetrics Helm chart."
  type        = string
  default     = "vm-cluster"
}

variable "chart_version" {
  description = "The version of the VictoriaMetrics chart to deploy."
  type        = string
  default     = "0.17.7"
}

variable "namespace" {
  description = "The Kubernetes namespace in which to deploy VictoriaMetrics."
  type        = string
  default     = "monitoring"
}

variable "create_namespace" {
  description = "Whether to create the namespace if it does not exist."
  type        = bool
  default     = true
}

# CloudWatch agent configuration
variable "cloudwatch_iam_role_arn" {
  description = "The ARN of the IAM role for CloudWatch agent."
  type        = string
}

# ==================================================================

# ALB Ingress Controller settings
variable "vmauth_ingress_cert_arn" {
  description = "The ARN of the ACM certificate to use for the VMAuth ingress."
  type        = string
}

variable "vmauth_ingress_host" {
  description = "The host for the VMAuth ingress."
  type        = string
}

# Enable/disable components
variable "vmauth_enabled" {
  description = "Enable the vmauth component."
  type        = bool
  default     = true
}

variable "vminsert_enabled" {
  description = "Enable the vminsert component."
  type        = bool
  default     = true
}

variable "vmselect_enabled" {
  description = "Enable the vmselect component."
  type        = bool
  default     = true
}

variable "vmstorage_enabled" {
  description = "Enable the vmstorage component."
  type        = bool
  default     = true
}

variable "vmalert_enabled" {
  description = "Enable the vmalert component."
  type        = bool
  default     = true
}

variable "alertmanager_enabled" {
  description = "Enable the alertmanager component."
  type        = bool
  default     = true
}

# Persistent Volume settings for vmstorage
variable "vmstorage_pv_enabled" {
  description = "Enable persistent volume for vmstorage."
  type        = bool
  default     = true
}

variable "vmstorage_pv_size" {
  description = "The size of the persistent volume for vmstorage."
  type        = string
  default     = "200Gi"
}

variable "vmstorage_retention_period" {
  description = "The retention period for vmstorage."
  type        = string
  default     = "30d" # 1 month
  # Possible units character: h(ours), d(ays), w(eeks), y(ears), if no unit character specified - month. The minimum retention period is 24h. See these [docs](https://docs.victoriametrics.com/single-server-victoriametrics/#retention)
}

# Replica counts
variable "vmauth_replica_count" {
  description = "Number of replicas for the vmauth component."
  type        = number
  default     = 2
}

variable "vminsert_replica_count" {
  description = "Number of replicas for the vminsert component."
  type        = number
  default     = 2
}

variable "vmselect_replica_count" {
  description = "Number of replicas for the vmselect component."
  type        = number
  default     = 2
}

variable "vmstorage_replica_count" {
  description = "Number of replicas for the vmstorage component."
  type        = number
  default     = 2
}

# VMAuth settings
variable "vmauth_ingester_user_password" {
  description = "The password for the vmauth ingester user."
  type        = string
}

variable "vmauth_querier_user_password" {
  description = "The password for the vmauth querier user."
  type        = string
}
