terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.17.0" # or any 2.x you wish
    }
  }
}

provider "kubernetes" {
  host                   = module.eks_victoria_metrics.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks_victoria_metrics.cluster_certificate_authority_data)
  token                  = module.eks_victoria_metrics.cluster_token

  exec {
    api_version = "client.authentication.k8s.io/v1"
    command     = "aws"
    args = [
      "eks", "get-token", "--cluster-name", var.eks_cluster_name
    ]
  }
}

provider "helm" {
  kubernetes {
    host                   = module.eks_victoria_metrics.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks_victoria_metrics.cluster_certificate_authority_data)
    token                  = module.eks_victoria_metrics.cluster_token
  }
}
