# ==============================================================================
# Domain Name
# ==============================================================================

module "victoria_metrics_acm_certificate" {
  source = "../aws_acm_certificate_cloudflare"

  domain_name               = var.vmauth_alb_domain_name
  cloudflare_zone_id        = "8e0e78445380f3394040f1bfaf93b74a"
  subject_alternative_names = []
  tags                      = var.default_tags
}

module "victoria_metrics_alb_dns" {
  source                 = "../cloudflare"
  cloudflare_zone_id     = "8e0e78445380f3394040f1bfaf93b74a"
  cloudflare_record_name = var.vmauth_alb_domain_name

  cloudflare_record_value = module.victoria_metrics_cluster.alb_endpoint
  cloudflare_record_type  = "CNAME"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
}

# ==============================================================================
# Load Balancer Controller
# ==============================================================================

module "aws_lb_controller_helm" {
  source = "../helm_aws_lb_controller"

  eks_cluster_name            = module.eks_victoria_metrics.cluster_name
  lb_controller_irsa_role_arn = module.eks_victoria_metrics.lb_controller_irsa_role_arn
}

# ==============================================================================
# EKS Cluster
# ==============================================================================

module "eks_victoria_metrics" {
  source = "../eks_victoria_metrics"

  cluster_name               = var.eks_cluster_name
  eks_version                = "1.32"
  subnet_ids                 = var.eks_subnet_ids
  cluster_security_group_ids = var.eks_security_group_ids

  # Node group scaling & instance settings
  desired_size   = 2
  min_size       = 1
  max_size       = 4
  instance_types = ["m5.2xlarge"]
  ami_type       = "BOTTLEROCKET_x86_64"
  capacity_type  = "ON_DEMAND"

  tags = var.default_tags
}

# ==============================================================================
# Helm Charts
# ==============================================================================

module "victoria_metrics_cluster" {
  source = "../helm_victoria_metrics_cluster"

  vmauth_ingress_host           = var.vmauth_alb_domain_name
  vmauth_ingress_cert_arn       = module.victoria_metrics_acm_certificate.certificate_arn
  vmauth_ingester_user_password = data.vault_generic_secret.victoria_metrics_secrets.data["user_ingester_password"]
  vmauth_querier_user_password  = data.vault_generic_secret.victoria_metrics_secrets.data["user_querier_password"]

  cloudwatch_iam_role_arn = module.cloudwatch_agent.cloudwatch_agent_irsa_role_arn

  # Override helm values if needed
  vmauth_replica_count    = 2
  vminsert_replica_count  = 2
  vmselect_replica_count  = 2
  vmstorage_replica_count = 1 # set to 1 so we don't need 2 EBS volumes
  vmstorage_pv_size       = "2Ti"
}

module "cloudwatch_agent" {
  source = "../helm_cloudwatch_agent"

  eks_cluster_name  = module.eks_victoria_metrics.cluster_name
  oidc_provider_arn = module.eks_victoria_metrics.oidc_provider_arn
  tags              = var.default_tags
}

# ==============================================================================
# Alarms
# ==============================================================================

# High CPU Usage
resource "aws_cloudwatch_metric_alarm" "cpu_usage_high" {
  alarm_name          = "${module.eks_victoria_metrics.cluster_name}-HighCPUUsage"
  alarm_description   = "Triggers if CPU usage > 80% on any node in ${module.eks_victoria_metrics.cluster_name} cluster"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  threshold           = 80
  period              = 60
  statistic           = "Average"
  namespace           = "ContainerInsights"
  metric_name         = "node_cpu_utilization"

  dimensions = {
    ClusterName = module.eks_victoria_metrics.cluster_name
  }

  alarm_actions = [
    var.alarm_sns_topic_arn
  ]
  ok_actions = [
    var.alarm_sns_topic_arn
  ]
}

# High Memory Usage
resource "aws_cloudwatch_metric_alarm" "memory_usage_high" {
  alarm_name          = "${module.eks_victoria_metrics.cluster_name}-HighMemoryUsage"
  alarm_description   = "Triggers if memory usage > 80% on any node in ${module.eks_victoria_metrics.cluster_name} cluster"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  threshold           = 80
  period              = 60
  statistic           = "Average"
  namespace           = "ContainerInsights"
  metric_name         = "node_memory_utilization"

  dimensions = {
    ClusterName = module.eks_victoria_metrics.cluster_name
  }

  alarm_actions = [
    var.alarm_sns_topic_arn
  ]
  ok_actions = [
    var.alarm_sns_topic_arn
  ]
}

# High Container Restarts
resource "aws_cloudwatch_metric_alarm" "container_restart_count" {
  alarm_name          = "${module.eks_victoria_metrics.cluster_name}-HighContainerRestarts"
  alarm_description   = "Triggers if container restarts exceed threshold in the EKS cluster"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  threshold           = 5
  period              = 300
  statistic           = "Sum"
  namespace           = "ContainerInsights"
  metric_name         = "pod_number_of_container_restarts"

  dimensions = {
    ClusterName = module.eks_victoria_metrics.cluster_name
  }

  alarm_actions = [
    var.alarm_sns_topic_arn
  ]
  ok_actions = [
    var.alarm_sns_topic_arn
  ]
}

# EBS Disk Usage
resource "aws_cloudwatch_metric_alarm" "disk_usage_high" {
  alarm_name          = "${module.eks_victoria_metrics.cluster_name}-DiskUsageHigh"
  alarm_description   = "Triggers if the victoria metrics EBS volume exceeds 80%"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  threshold           = 80
  period              = 300
  statistic           = "Average"
  namespace           = "EBS/Disk"
  metric_name         = "disk_used_percent"

  dimensions = {
    UsedFor = "VictoriaMetrics"
    path    = "/storage"
    device  = "nvme2n1"
    fstype  = "ext4"
  }

  alarm_actions = [
    var.alarm_sns_topic_arn
  ]
  ok_actions = [
    var.alarm_sns_topic_arn
  ]
}
