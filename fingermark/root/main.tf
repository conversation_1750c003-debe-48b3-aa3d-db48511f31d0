module "iam_password_policy" {
  source = "../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DevAccess", "DeployerAccess", "StorageAccess", "FinanceAccess", "QuicksightAdminAccess", "DataScientist"]
}

module "vpc_flow_logs_ap_southeast_2" {
  providers = {
    aws = aws
  }
  source          = "../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags = {
    Terraform = "True"
    Stack     = "SOC2"
    Squad     = "Platform"
  }
}

module "vpc_flow_logs_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source          = "../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags = {
    Terraform = "True"
    Stack     = "SOC2"
    Squad     = "Platform"
  }
}

module "scp_enforce_tagged_resources" {
  source       = "../../modules/scp_enforce_tagged_resources"
  target_ou_id = aws_organizations_organizational_unit.eyecue_prod.id
}

module "identity_center" {
  source = "../../modules/identity_center"
  permission_sets = [
    {
      name               = "AdminAccess",
      description        = "AdminAccess Policy",
      policy_name        = "AdminAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "PowerAccess",
      description        = "PowerAccess Permission Set for Devs with higher privilege",
      policy_name        = "PowerAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "DevAccess",
      description        = "DevAccess Permission Set for Developers for basic privilege",
      policy_name        = "DevAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "KommisjonAccess",
      description        = "KommisjonAccess Permission Set for Kommisjon",
      policy_name        = "KommisjonAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "KommisjonPortForwardAccess",
      description        = "Kommisjon Port Forward Access Permission Set",
      policy_name        = "KommisjonPortForwardAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "StorageAccess",
      description        = "StorageAccess Permission Set",
      policy_name        = "StorageAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "DeployerAccess",
      description        = "DeployerAccess Permission Set for Developers for deployment accounts",
      policy_name        = "DeployerAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "FinanceAccess",
      description        = "FinanceAccess Permission Set",
      policy_name        = "FinanceAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "ReadOnlyAccess",
      description        = "ReadOnlyAccess Permission Set",
      policy_name        = "ReadOnlyAccess",
      aws_managed_policy = true
    },
    {
      name               = "QuicksightAdminAccess",
      description        = "QuicksightAdminAccess Permission Set",
      policy_name        = "QuicksightAdminAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "DataScientistAccess",
      description        = "DataScientist Permission Set",
      policy_name        = "DataScientistPolicy",
      aws_managed_policy = false
    },
    {
      name               = "SupportAccess",
      description        = "SupportAccess Permission Set",
      policy_name        = "SupportAccessPolicy",
      aws_managed_policy = false
    },
    {
      name               = "ICXeedAccess",
      description        = "ICXeed External Team Access for project collaboration",
      policy_name        = "ICXeedAccessPolicy",
      aws_managed_policy = false
    }
  ]
  permission_set_associations = local.permission_set_associations
}


module "aws_config_organization_rules" {
  source = "../../modules/aws_config_organization_rules"

  canned_rule_set = "baseline"

  notify_emails = [
    "<EMAIL>",
    "<EMAIL>",    # Temporary for verifying Terraform deployment
    "<EMAIL>", # Temporary for verifying Terraform deployment
  ]

  global_excluded_accounts = [
    # Temporary exclude accounts with not yet enabled AWS Config recorder:
    "************", # fs00231
    "************", # fs01076
    "************", # fs03978
    "************", # fs09212
    "************", # fs07026
    "************", # fs03247

    # Exclude suspended accounts
    "************", # CV - PROD - me-south-1 - reds
    "************", # Fernando FM

    # Exclude to be deleted accounts: supersonic
    "************", # AWS - PROD - EA - AMERICANA
    "************", # AWS - PROD - SUPERSONIC
    "************", # S2 - PROD - RBNZ

    # Exclude to be deleted accounts: northvue
    "************", # northvue-us-east-1-prod
    "************", # northvue-us-east-1-stg
    "************", # northvue-us-east-1-dev

    # Exclude to be deleted accounts - dmbo
    "************", # samsung-dmbo-prod
  ]

  tags = {}
  default_tags = {
    Terraform = "true"
    Stack     = "monitoring"
  }
}

# ===============================================
# SOC2 Compliance
# ===============================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source                  = "../../modules/vanta"
  identity_center_enabled = true
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source               = "../../modules/sqs_monitoring"
  lambda_function_name = "sqs-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge({}, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = {}
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../modules/cw_log_retention"
  retention_days = 365
  tags           = {}
  default_tags = {
    Terraform = "true"
    Stack     = "soc2"
    Squad     = "Platform"
  }
}

module "cw_log_retention_us_east_1" {
  source         = "../../modules/cw_log_retention"
  retention_days = 365
  tags           = {}
  default_tags = {
    Terraform = "true"
    Stack     = "soc2"
    Squad     = "Platform"
  }

  providers = {
    aws = aws.us-east-1
  }
}

module "cw_log_retention_us_west_2" {
  source         = "../../modules/cw_log_retention"
  retention_days = 365
  tags           = {}
  default_tags = {
    Terraform = "true"
    Stack     = "soc2"
    Squad     = "Platform"
  }

  providers = {
    aws = aws.us-west-2
  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
      us_east_1 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_us_east_1,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_us_east_1
        )
      }
      us_west_2 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_us_west_2,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_us_west_2
        )
      }
    }
  }
  elb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        alb_config = {
          "lb-meshcentral" = { lb_name = "lb-meshcentral", tg_names = ["tg-meshcentral-CIRA", "tg-meshcentral-https"] }
        }
      }
    }
  }
}

module "dynamodb_cw_alarms_defaults_ap_southeast_2" {
  source                                 = "../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config

  tags = {}
  default_tags = {
    Terraform = "true"
    Stack     = "monitoring"
  }
}

module "dynamodb_cw_alarms_defaults_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source                                 = "../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.us_east_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.us_east_1.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.us_east_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.us_east_1.gsis_config

  tags = {}
  default_tags = {
    Terraform = "true"
    Stack     = "monitoring"
  }
}

module "dynamodb_cw_alarms_defaults_us_west_2" {
  providers = {
    aws = aws.us-west-2
  }
  source                                 = "../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["us-west-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.us_west_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.us_west_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.us_west_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.us_west_2.gsis_config

  tags = {}
  default_tags = {
    Terraform = "true"
    Stack     = "monitoring"
  }
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "DevOpsLand"                      = { instance_tags = { Name = "DevOpsLand" } }
    "Jenkins"                         = { instance_tags = { Name = "Jenkins" } }
    "Matrix_Web_Master"               = { instance_tags = { Name = "Matrix_Web_Master" } }
    "Matrix_Web_UAT"                  = { instance_tags = { Name = "Matrix_Web_UAT" } }
    "MongoDB_Master_01"               = { instance_tags = { Name = "MongoDB_Master_01" } }
    "SmartCache_ADHB_Staging_Windows" = { instance_tags = { Name = "SmartCache_ADHB_Staging_Windows" } }
    "VPN_AWS"                         = { instance_tags = { Name = "VPN_AWS" } }
    "VPN_WDHB"                        = { instance_tags = { Name = "VPN_WDHB" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

module "ec2_instance_cw_alarms_us_east_1" {
  providers      = { aws = aws.us-east-1 }
  source         = "../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "UniFi Controller" = { instance_tags = { Name = "UniFi Controller" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

module "elb_cw_alarms_ap_southeast_2" {
  source                                        = "../../modules/elb_cw_alarms"
  sns_topic_arns                                = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_alb_healthy_host_count        = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_unhealthy_host_count      = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_target_response_time      = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_httpcode_elb_5xx_count    = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_httpcode_target_5xx_count = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  tags                                          = {}
  default_tags = {
    Terraform = "true"
    Stack     = "monitoring"
  }
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  }
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
