AWS_REGION = "ap-southeast-2"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "drive-thru-transactions"                 = { table_name = "drive-thru-transactions" },
  "Metrics-b2f2ezvm5nea3ht5q4p5refeta-prod" = { table_name = "Metrics-b2f2ezvm5nea3ht5q4p5refeta-prod" },
  "peopleportal_device_group"               = { table_name = "peopleportal_device_group" },
  "peopleportal_media"                      = { table_name = "peopleportal_media" },
  "peopleportal_media_file"                 = { table_name = "peopleportal_media_file" },
  "peopleportal_media_group"                = { table_name = "peopleportal_media_group" },
  "peopleportal_user_group"                 = { table_name = "peopleportal_user_group" },
  "serial_validation_request"               = { table_name = "serial_validation_request" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "cv-things-configuration-prod" = { table_name = "cv-things-configuration-prod" },
  "eyecue-sites-configuration"   = { table_name = "eyecue-sites-configuration" },
  "eyecue-sites-container"       = { table_name = "eyecue-sites-container" },
  "eyecue-things-certificates"   = { table_name = "eyecue-things-certificates" },
  "eyecue-things-connections"    = { table_name = "eyecue-things-connections" },
}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {
  "eyecue-things-certificates-certificate" = { table_name = "eyecue-things-certificates", index_name = "certificate" },
}
dynamodb_cw_alarms_defaults_tables_us_west_2 = {
  "matrix-platform-messages" = { table_name = "matrix-platform-messages" }
}
dynamodb_cw_alarms_defaults_gsis_us_west_2 = {}
