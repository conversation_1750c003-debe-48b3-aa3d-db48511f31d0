variable "AWS_REGION" {
  default = "ap-southeast-2"
}


variable "org_paths" {
  description = <<-DOC
    Map of organizational unit paths to account IDs. This is used to determine the accounts that
    should be included in the AWS SSO permission sets.
  DOC
  type        = map(string)
  default = {
    "data_dev"      = "ou-dfjw-me27jmfy"
    "data_prod"     = "ou-dfjw-g9keykaq"
    "eyecue_dev"    = "ou-dfjw-78jpmu5e"
    "eyecue_prod"   = "ou-dfjw-fvxam8hu"
    "northvue_dev"  = "ou-dfjw-2yrqzm4r"
    "northvue_stg"  = "ou-dfjw-36fcjnhf"
    "northvue_prod" = "ou-dfjw-icihizob"
    "platform"      = "ou-dfjw-wwdqga5u"
    "fm_prod"       = "ou-dfjw-zmpktd0m"
    "dmbo"          = "ou-dfjw-1few957d"
    "supersonic"    = "ou-dfjw-0a8zhzi5"
    "support"       = "ou-dfjw-6umds1mv"
  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}

variable "dynamodb_cw_alarms_defaults_tables_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB tables in ap-southeast-2 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in ap-southeast-2 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_tables_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB tables in us-east-1 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in us-east-1 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_tables_us_west_2" {
  description = <<-DOC
    Map of additional DynamoDB tables in us-west-2 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_us_west_2" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in us-west-2 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}
