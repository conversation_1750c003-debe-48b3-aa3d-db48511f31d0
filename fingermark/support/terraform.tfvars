
AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "fm-support"
CLIENT_ACRONYM = "fm-support"
COUNTRY        = "au"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "fingermark-dev-contact-survey-surveys-config"   = { table_name = "fingermark-dev-contact-survey-surveys-config" },
  "fingermark-dev-contact-survey-surveys-results"  = { table_name = "fingermark-dev-contact-survey-surveys-results" },
  "fingermark-prod-contact-survey-surveys-config"  = { table_name = "fingermark-prod-contact-survey-surveys-config" },
  "fingermark-prod-contact-survey-surveys-results" = { table_name = "fingermark-prod-contact-survey-surveys-results" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
