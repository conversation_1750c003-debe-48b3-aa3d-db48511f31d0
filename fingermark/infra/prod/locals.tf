locals {
  # ==========================================
  # VPC
  # ==========================================
  vpn_ips = ["**************/32", "${aws_eip.wireguard.public_ip}/32"]

  # ===============================================
  # Egress VPN Gateway (For customer firewalls)
  # ===============================================
  vpn_dns_name = "egress-vpn.eyecue.fingermark.tech"

  # ===============================================
  # CloudWatch Alarms
  # ===============================================
  cw_alarm_notifications_sns_topic = {
    notify_emails = [
      "<EMAIL>",
    ]
  }
  rds_cw_alarms = {
    "icinga2_master_db" = {
      # Note: `module.icinga2_master_db` doesn't output `DBInstanceIdentifier` hence extracting it from the ARN
      identifier = regex(".*:db:([^:]+)$", module.icinga2_master_db.db_instance_arn)[0]
    }
  }
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
    }
  }
  elb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        alb_config = {
          "InfraLB"                          = { lb_name = "InfraLB", tg_names = ["GrafanaALB", "Icinga2MasterConfigTG", "Icinga2TG"] }
          "k8s-atlantis-atlantis-3029588845" = { lb_name = "k8s-atlantis-atlantis-3029588845", tg_names = ["k8s-atlantis-atlantis-264dd437a1"] }
          "k8s-awx-awxingre-2a6a396741"      = { lb_name = "k8s-awx-awxingre-2a6a396741", tg_names = ["k8s-awx-fingerma-e89618f748"] }
          "k8s-infraapi-infraapi-c65dcec973" = { lb_name = "k8s-infraapi-infraapi-c65dcec973", tg_names = ["k8s-infraapi-infraapi-225a4af964"] }
          "k8s-monitori-vmcluste-2b057c78b5" = { lb_name = "k8s-monitori-vmcluste-2b057c78b5", tg_names = ["k8s-monitori-vmcluste-eefed86d9d"] }
        }
        nlb_config = {
          "fingermark-infra-k8s-cluster-nginx-ctrl-nlb" = { lb_name = "acdff9ce4b3b34fd48704c4c8715c736", tg_names = ["k8s-ingressn-ingressn-b09f001ce3", "k8s-ingressn-ingressn-d33c1e3ab8"] }
        }
        clb_config = {
          "ssh-node"                                    = { lb_name = "ssh-node" }
          "fingermark-infra-k8s-cluster-nginx-ctrl-clb" = { lb_name = "acdff9ce4b3b34fd48704c4c8715c736" }
        }
      }
      us_east_1 = {
        alb_config = {
          "k8s-monitori-vmcluste-30443d80ec" = { lb_name = "k8s-monitori-vmcluste-30443d80ec", tg_names = ["k8s-monitori-vmcluste-9257799537"] }
        }
      }
    }
  }

  # ==========================================
  # Grafana Server
  # ==========================================
  grafana_web_env_vars = [
    {
      "name" : "GF_INSTALL_PLUGINS",
      "value" : "grafana-worldmap-panel, grafana-piechart-panel, grafana-googlesheets-datasource, marcusolsson-json-datasource, grafana-clock-panel, grafana-redshift-datasource",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_USER",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_USER"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SECURITY_ADMIN_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_SECURITY_ADMIN_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_DATABASE_TYPE",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_TYPE"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_HOST",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_HOST"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_NAME",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_NAME"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SECURITY_DISABLE_BRUTE_FORCE_LOGIN_PROTECTION",
      "value" : "false",
      "type" : "SecureString"
    },
    {
      "name" : "GF_LIVE_ALLOWED_ORIGINS"
      "value" : "${data.vault_generic_secret.infra.data["GF_LIVE_ALLOWED_ORIGINS"]}",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_ENABLED",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_DEFAULT_INSTANCE_NAME",
      "value" : "${data.vault_generic_secret.infra.data["GF_DEFAULT_INSTANCE_NAME"]}",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_HOST",
      "value" : "smtp.sendgrid.net:587",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_USER",
      "value" : "apikey",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_SMTP_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SMTP_FROM_ADDRESS",
      "value" : "<EMAIL>",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_FROM_NAME",
      "value" : "Grafana",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_SKIP_VERIFY",
      "value" : "false",
      "type" : "String"
    },
    {
      "name" : "GF_SERVER_ROOT_URL",
      "value" : "https://grafana.infra.fingermark.tech/",
      "type" : "String"
    }
  ]
}
