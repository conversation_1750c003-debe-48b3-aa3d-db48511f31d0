
# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "KnowledgeBase"                    = { table_name = "KnowledgeBase" },
  "eyecue-inventory-table"           = { table_name = "eyecue-inventory-table" },
  "camera-connectivity-alerts-dev"   = { table_name = "camera-connectivity-alerts-dev" },
  "eyecue-provisioner-servers"       = { table_name = "eyecue-provisioner-servers" },
  "server-connectivity-alert-status" = { table_name = "server-connectivity-alert-status" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "eyecue-inventory-table" = { table_name = "eyecue-inventory-table" }
}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {}
