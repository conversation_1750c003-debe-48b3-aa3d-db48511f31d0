locals {
  # ===============================================
  # Egress VPN Gateway (For customer firewalls)
  # ===============================================
  vpn_dns_name = "egress-vpn-dev.eyecue.fingermark.tech"

  # ===============================================
  # CloudWatch Alarms
  # ===============================================
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
      us_east_1 = {
        tables_config = merge(
          var.dynamodb_cw_alarms_defaults_tables_us_east_1,
        )
        gsis_config = merge(
          var.dynamodb_cw_alarms_defaults_gsis_us_east_1
        )
      }
    }
  }
  elb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        nlb_config = {
          "eyecue-whitelist-vpn-dev-lb" = { lb_name = "eyecue-whitelist-vpn-dev-lb", tg_names = ["eyecue-whitelist-vpn-dev-tg"] }
        }
        alb_config = {
          "teleport-infra-dev-alb" = { lb_name = "teleport-infra-dev-alb", tg_names = ["teleport-infra-dev-tg"] }
        }
      }
    }
  }
}
