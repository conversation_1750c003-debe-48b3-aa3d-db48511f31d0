
module "aggregateddata_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "aggregated-data"
  tags     = var.default_tags
}

module "alertmanager_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "alertmanager"
  tags     = var.default_tags
}

module "alprpipeline_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "alpr-pipeline"
  tags     = var.default_tags
}

module "argocd_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "argocd"
  tags     = var.default_tags
}

module "assetshistory_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "assets-history"
  tags     = var.default_tags
}

module "backgroundimagecollection_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "background-image-collection"
  tags     = var.default_tags
}

module "bestshotcapturer_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "best-shot-capturer"
  tags     = var.default_tags
}

module "bestshotsaver_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "best-shot-saver"
  tags     = var.default_tags
}

module "blackbox_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "blackbox"
  tags     = var.default_tags
}

module "cameraassetcollection_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-asset-collection"
  tags     = var.default_tags
}

module "cameraconfigchanger_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-config-changer"
  tags     = var.default_tags
}

module "cameraconfig_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-config"
  tags     = var.default_tags
}

module "camerareader_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-reader"
  tags     = var.default_tags
}

module "decisionchain_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "decision-chain"
  tags     = var.default_tags
}

module "deepstream_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "deep-stream"
  tags     = var.default_tags
}

module "deepstreammsgdispatcher_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "deepstream-msg-dispatcher"
  tags     = var.default_tags
}

module "eventrecorder_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "event-recorder"
  tags     = var.default_tags
}

module "eyecueargus_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-argus"
  tags     = var.default_tags
}

module "eyecueassembler_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-assembler"
  tags     = var.default_tags
}

module "eyecueclassifier_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-classifier"
  tags     = var.default_tags
}

module "eyecuecloudsync_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-cloud-sync"
  tags     = var.default_tags
}

module "eyecuegrafana_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-grafana"
  tags     = var.default_tags
}

module "eyecueshinobiconfig_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-shinobi-config"
  tags     = var.default_tags
}

module "ecr_eyecue_store_state_builder" {
  source                   = "../../../../modules/ecr"
  ecr_name                 = "eyecue-store-state-builder"
  ecr_image_tag_mutability = "IMMUTABLE"
  tags                     = merge(var.default_tags, { Stack = "Eyecue" })
}

module "eyeqbenchmarkingrunner_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-benchmarking-runner"
  tags     = var.default_tags
}

module "eyeqcameradatacapture_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-camera-data-capture"
  tags     = var.default_tags
}

module "eyeqcredentials_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-credentials"
  tags     = var.default_tags
}

module "eyeqdetector_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-detector"
  tags     = var.default_tags
}

module "eyeqmetrics_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-metrics"
  tags     = var.default_tags
}

module "eyeqmosaic_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-mosaic"
  tags     = var.default_tags
}

module "eyeqs3syncker_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-s3syncker"
  tags     = var.default_tags
}

module "eyeqserver_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-server"
  tags     = var.default_tags
}

module "eyeqtessera_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-tessera"
  tags     = var.default_tags
}

module "eyeqtracker_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-tracker"
  tags     = var.default_tags
}

module "framegrabber_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "framegrabber"
  tags     = var.default_tags
}

module "lpr_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "lpr"
  tags     = var.default_tags
}

module "minio_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "minio"
  tags     = var.default_tags
}

module "mongoframeinserter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "mongo-frame-inserter"
  tags     = var.default_tags
}

module "nginx_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nginx"
  tags     = var.default_tags
}

module "netplanwatchdog_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "netplan-watchdog"
  tags     = var.default_tags
}

module "rabbitmq_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "rabbitmq"
  tags     = var.default_tags
}

module "reacquirementreport_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "reacquirement-report"
  tags     = var.default_tags
}

module "reacquirementtimecollection_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "reacquirement-time-collection"
  tags     = var.default_tags
}

module "roisuggestorjobs_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "roi-suggestor-jobs"
  tags     = var.default_tags
}

module "telegraf_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "telegraf"
  tags     = var.default_tags
}

module "trainingdatacapture_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "training-data-capture"
  tags     = var.default_tags
}

module "tritonserver_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "tritonserver"
  tags     = var.default_tags
}

module "victoriametrics_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "victoria-metrics"
  tags     = var.default_tags
}

module "vmagent_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "vmagent"
  tags     = var.default_tags
}

module "vmalert_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "vmalert"
  tags     = var.default_tags
}

module "validationtool_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "validation-tool"
  tags     = var.default_tags
}

module "eyecue_iam_provider_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-iam-provider"
  tags     = var.default_tags
}

module "moonfire_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-moonfire-nvr"
  tags     = var.default_tags
}

module "moonfire_exporter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-moonfire-exporter"
  tags     = var.default_tags
}

module "tracker_features_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-tracker-features"
  tags     = var.default_tags
}

module "monitor_recorder_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-monitor-recorder"
  tags     = var.default_tags
}

module "config_reloader_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-config-reloader"
  tags     = var.default_tags
}

module "event_handler_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-event-handler"
  tags     = var.default_tags
}

module "edge_metadata_gatekeeper_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "edge-metadata-gatekeeper"
  tags     = var.default_tags
}

module "camera_displacement_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-displacement"
  tags     = var.default_tags
}

module "eyecue_weights_sync_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-weights-sync"
  tags     = var.default_tags
}

module "eyecue_camera_metrics_exporter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-camera-metrics-exporter"
  tags     = var.default_tags
}
