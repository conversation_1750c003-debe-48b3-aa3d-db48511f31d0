###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

variable "AWS_REGION" {
  default = "us-east-1"
}
variable "ENVIRONMENT" {}

variable "CLIENT_NAME" {
  type    = string
  default = "cfa"
}

variable "CLIENT_ACRONYM" {
  type    = string
  default = "cfa"
}

variable "COUNTRY" {
  type    = string
  default = "us"
}

variable "COUNTRY_FULL" {
  type    = string
  default = "usa"
}

variable "KEYBASE" {
  type    = string
  default = "keybase:fingermark"
}

variable "sns_topics" {
  type = map(object({
    topic_name : string
    display_name : string
    tags : map(string)
    subscriptions : optional(object({
      protocol : string
      endpoint : string
    }), null)
  }))
  description = "List of SNS topics to create"
  default = {
    cloudwatch-alarm-alert-helper-topic : {
      topic_name   = "cloudwatch-alarm-alert-helper-topic"
      display_name = "Send Alert From SNS to eyecue-app-team-alerts Slack Channel"
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      subscriptions = {
        protocol = "email"
        endpoint = "<EMAIL>"
      }
    },
  }
}

## RDS Module ###
variable "RDS_MASTER_INSTANCE_CLASS" {
  default = "db.t3.micro"
}


### NETWORK Module ###

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "prod"
  }
}

variable "customer" {
  default     = "cfa"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "eyecue"
  description = "Fingermark Product"
  type        = string
}


### kinesis data stream Module ###
variable "kinesis_data_stream_retention_period" {
  type    = number
  default = 24
}
variable "kinesis_data_stream_stream_mode" {
  type    = string
  default = "ON_DEMAND"
}

variable "redshift_aws_account_ids_roles" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_role_us_east_1" },
    { account_id = "************", role_name = "redshift_stream_role_us_east_1" }
  ]
}

variable "kinesis_stream_name_roi" {
  type    = string
  default = "ds-cfa-eyecue-roi"
}

variable "kinesis_stream_name_hvi" {
  type    = string
  default = "ds-cfa-eyecue-hvi"
}

variable "kinesis_stream_name_aggregate" {
  type    = string
  default = "ds-cfa-eyecue-aggregate"
}

variable "kinesis_stream_name_departure" {
  type    = string
  default = "ds-cfa-eyecue-departure"
}

variable "kinesis_stream_name" {
  type    = string
  default = "ds-cfa-eyecue-eventstream"
}

variable "kinesis_iot_topic_rules_config" {
  description = "Configuration for each IoT topic rule"
  type = map(object({
    name : string
    sql : string
    stream_name : string
    description : string
    enabled : bool
  }))
  default = {
    "roi_eyecue_iot_kinesis_eventstream" = {
      name        = "roi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'roi' as event_type, 'cfa-usa' as client_name FROM '/eyeq/roievent/#'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "hvi_eyecue_iot_kinesis_eventstream" = {
      name        = "hvi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT HVI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'hvi' as event_type, 'cfa-usa' as client_name FROM '/eyeq/hci/#'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "hvi_looptimer_eyecue_iot_kinesis_eventstream" = {
      name        = "hvi_looptimer_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT unregistered HVI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'hvi-looptimer' as event_type, 'cfa-usa' as client_name FROM '/eyeq/hci-looptimer/#'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "aggregated_eyecue_iot_kinesis_eventstream" = {
      name        = "aggregated_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Aggregated events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'aggregate' as event_type, 'cfa-usa' as client_name FROM '/eyeq/vehicle-aggregated-2-0/#'"
      stream_name = "ds-cfa-eyecue-aggregate"
      enabled     = true
    },
    "departure_eyecue_iot_kinesis_eventstream" = {
      name        = "departure_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'departure' as event_type, 'cfa-usa' as client_name FROM '/eyeq/departure/#'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "arrival_eyecue_iot_kinesis_eventstream" = {
      name        = "arrival_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Arrival events to Kinesis Data Stream"
      sql         = "SELECT *, arrival.site_id as store_id, 'arrival' as event_type, 'cfa-usa' as client_name FROM '/eyeq/arrival/#'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "danger_zone_eyecue_iot_kinesis_eventstream" = {
      name        = "danger_zone_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Danger Zone events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'danger-zone' as event_type, 'cfa-usa' as client_name FROM '/eyeq/danger-zones/+'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "lead_car_eyecue_iot_kinesis_eventstream" = {
      name        = "lead_car_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Lead Car Timer events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'lead-car' as event_type, 'cfa-usa' as client_name FROM '/eyeq/roicount/+'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "people_count_eyecue_iot_kinesis_eventstream" = {
      name        = "people_count_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT People Count events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'cfa-usa' as client_name FROM '/eyeq/employee-count/+'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "tet_eyecue_iot_kinesis_eventstream" = {
      name        = "tet_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT TET events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id,'tet' as event_type 'cfa-usa' as client_name FROM '/eyeq/aggregated-data/+'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "indoor_tracking_eyecue_iot_kinesis_eventstream" = {
      name        = "indoor_tracking_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Indoor events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'cfa-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/#'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
    "rolling_eyecue_iot_kinesis_eventstream" = {
      name        = "rolling_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Rolling events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'rolling' as event_type, 'cfa-usa' as client_name FROM '/eyeq/rolling/+'"
      stream_name = "ds-cfa-eyecue-eventstream"
      enabled     = true
    },
  }
}


variable "client_aws_account_details" {
  #CFA client AWS integration details
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "dev-fingermark-data-ingestion-kinesis-redshift-role" },
    { account_id = "************", role_name = "bdap-redshift-discovery" }, #cfabdapdev
    { account_id = "************", role_name = "bdap-redshift-discovery" }  #cfabdappod
  ]
}


variable "stream_name_list" {
  description = "List of stream types"
  type        = list(string)
  default     = ["eventstream"]
}

variable "client_granted_stream_resource_arns" {
  type = list(string)
  default = [
    "arn:aws:kinesis:us-east-1:************:stream/ds-cfa-eyecue-eventstream",
    "arn:aws:kinesis:us-east-1:************:stream/ds-cfa-eyecue-aggregate",
    "arn:aws:kinesis:us-east-1:************:stream/ds-cfa-usa-eyecue-drivethru-events"
  ]
}

variable "lambda_role_arn_suffixes" {
  description = "List of Lambda role ARN suffixes"
  type        = list(string)
  default = [
    "eyecue-mimir-blue-prod-ap-southeast-2-lambdaRole",
    "project-mimir-prod-prod-ap-southeast-2-lambdaRole",
    "eyecue-mimir-green-prod-ap-southeast-2-lambdaRole"
  ]
}

variable "databricks_role_arn_list" {
  type = list(string)
  default = [
    "arn:aws:iam::************:role/databricks-primary-general-node-role",
  ]
}

variable "databricks_role_prod_arn_list" {
  type = list(string)
  default = [
    "arn:aws:iam::************:role/databricks-primary-general-node-role"
  ]
}

variable "kinesis_client_name" {
  type    = string
  default = "cfa-usa"
}


locals {
  #Kinesis Stream Names
  journey_stream   = "ds-${var.kinesis_client_name}-eyecue-journey"
  drivethru_stream = "ds-${var.kinesis_client_name}-eyecue-drivethru-events"
  indoor_stream    = "ds-${var.kinesis_client_name}-eyecue-indoor-events"
}


locals {
  #IOT to Kinesis Routing Rules
  kinesis_iot_topic_rules_config = {
    "roi_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_roi"
      description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'roi' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/roievent/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic HVI - use as Interaction in Data Environment
    "interaction_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_interaction"
      description = "Topic Rule for Forwarding IoT Interaction(HVI) events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'interaction' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/hci/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "hvi_looptimer_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_hvi_loop_timer"
      description = "Topic Rule for Forwarding IoT unregistered HVI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'hvi-looptimer' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/hci-looptimer/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic Aggregate - use as Journey in Data Environment
    "journey_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_journey_stream_aggregate"
      description = "Topic Rule for Forwarding IoT Journey (Aggregated) events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'journey' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/vehicle-aggregated-2-0/#'"
      stream_name = local.journey_stream
      enabled     = true
    },
    "departure_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_departure"
      description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'departure' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/departure/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "arrival_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_arrival"
      description = "Topic Rule for Forwarding IoT Arrival events to Kinesis Data Stream"
      sql         = "SELECT *, arrival.site_id as store_id, 'arrival' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/arrival/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic Danger Zone use as Queue Zone in Data Environment
    "queue_zone_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_queue_zone"
      description = "Topic Rule for Forwarding IoT Queue Zone (Danger Zone) events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'queue-zone' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/danger-zones/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "lead_car_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_lead_car"
      description = "Topic Rule for Forwarding IoT Lead Car Timer events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'lead-car' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/roicount/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "people_count_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_people_count"
      description = "Topic Rule for Forwarding IoT People Count events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id,'employee-count' as event_type,'${var.kinesis_client_name}'  as client_name FROM '/eyeq/employee-count/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "tet_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_tet"
      description = "Topic Rule for Forwarding IoT TET events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id,'tet' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/aggregated-data/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "indoor_tracking_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_indoor_stream_tracking"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Indoor events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id,'indoor' as event_type, '${var.kinesis_client_name}' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/#'"
      stream_name = local.indoor_stream
      enabled     = true
    },
    "rolling_eyecue_iot_kinesis_eventstream" = {
      name        = "kinesis_drivethru_stream_rolling"
      description = "Topic Rule for Forwarding IoT Rolling events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'rolling' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/rolling/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "active_departure_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_active_departure"
      description = "Topic Rule for Forwarding IoT active Departure events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'active-departure-rate' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/active-departure-rate/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    }

  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}

variable "dynamodb_cw_alarms_defaults_tables_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB tables in ap-southeast-2 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in ap-southeast-2 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_tables_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB tables in us-east-1 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in us-east-1 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}
