
AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "mnz"
CLIENT_ACRONYM = "mnz"
COUNTRY        = "nzl"
ENVIRONMENT    = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "argus-connections"    = { table_name = "argus-connections" },
  "argus-messages"       = { table_name = "argus-messages" },
  "eyecue-helm-values"   = { table_name = "eyecue-helm-values" },
  "eyecue-things-shadow" = { table_name = "eyecue-things-shadow" },
  "eyecue-weights"       = { table_name = "eyecue-weights" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
