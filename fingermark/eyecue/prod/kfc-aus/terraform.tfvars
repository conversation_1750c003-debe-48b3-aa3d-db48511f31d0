
AWS_REGION                = "ap-southeast-2"
APP_STAGE                 = "prod"
KEYBASE                   = "keybase:fingermark"
CLIENT_NAME               = "kfc"
CLIENT_ACRONYM            = "kfc"
COUNTRY                   = "au"
RDS_MASTER_INSTANCE_CLASS = "db.t3.micro"
ENVIRONMENT               = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "argus-connections"          = { table_name = "argus-connections" }
  "argus-messages"             = { table_name = "argus-messages" }
  "chromebox-action-log"       = { table_name = "chromebox-action-log" }
  "eyecue-deployer-params"     = { table_name = "eyecue-deployer-params" }
  "eyecue-helm-values"         = { table_name = "eyecue-helm-values" }
  "eyecue-sites-configuration" = { table_name = "eyecue-sites-configuration" }
  "eyecue-sites-container"     = { table_name = "eyecue-sites-container" }
  "eyecue-things-shadow"       = { table_name = "eyecue-things-shadow" }
  "eyecue-weights"             = { table_name = "eyecue-weights" }
  "KFC-hourly-vehicle-count"   = { table_name = "KFC-hourly-vehicle-count" }
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
