
AWS_REGION     = "us-west-1"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "czp"
CLIENT_ACRONYM = "czp"
COUNTRY        = "us"
ENVIRONMENT    = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================

dynamodb_cw_alarms_defaults_tables_us_west_1 = {
  "argus-connections"    = { table_name = "argus-connections" },
  "argus-messages"       = { table_name = "argus-messages" },
  "eyecue-helm-values"   = { table_name = "eyecue-helm-values" },
  "eyecue-things-shadow" = { table_name = "eyecue-things-shadow" },
  "eyecue-weights"       = { table_name = "eyecue-weights" },
}
dynamodb_cw_alarms_defaults_gsis_us_west_1 = {}
