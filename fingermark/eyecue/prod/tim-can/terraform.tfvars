AWS_REGION                = "ca-central-1"
APP_STAGE                 = "prod"
KEYBASE                   = "keybase:fingermark"
CLIENT_NAME               = "tim"
CLIENT_ACRONYM            = "tim-can"
COUNTRY                   = "can"
RDS_MASTER_INSTANCE_CLASS = "db.t3.micro"
ENVIRONMENT               = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ca_central_1 = {
  "argus-connections"    = { table_name = "argus-connections" },
  "argus-messages"       = { table_name = "argus-messages" },
  "eyecue-helm-values"   = { table_name = "eyecue-helm-values" },
  "eyecue-things-shadow" = { table_name = "eyecue-things-shadow" },
  "eyecue-weights"       = { table_name = "eyecue-weights" },
}
dynamodb_cw_alarms_defaults_gsis_ca_central_1 = {}
