AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "mcd"
CLIENT_ACRONYM = "mcd"
COUNTRY        = "au"
ENVIRONMENT    = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "argus-connections"                = { table_name = "argus-connections" },
  "argus-messages"                   = { table_name = "argus-messages" },
  "dashboard-mcd-site-configuration" = { table_name = "dashboard-mcd-site-configuration" },
  "eyecue-data-capture"              = { table_name = "eyecue-data-capture" },
  "eyecue-helm-values"               = { table_name = "eyecue-helm-values" },
  "eyecue-sites-configuration"       = { table_name = "eyecue-sites-configuration" },
  "eyecue-sites-container"           = { table_name = "eyecue-sites-container" },
  "eyecue-things-shadow"             = { table_name = "eyecue-things-shadow" },
  "eyecue-weights"                   = { table_name = "eyecue-weights" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
