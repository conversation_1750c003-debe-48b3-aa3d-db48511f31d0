###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

### Dynamic iot topic rules for kinesis data stream ###
### dynamic kinesis stream name based on the client name to use in iot topic rule ###
locals {
  #Kinesis Stream Names
  journey_stream          = "ds-${var.kinesis_client_name}-eyecue-journey"
  drivethru_stream        = "ds-${var.kinesis_client_name}-eyecue-drivethru-events"
  indoor_stream           = "ds-${var.kinesis_client_name}-eyecue-indoor-events"
  aggregate_stream_name   = "ds-mcd-eyecue-aggregate"
  eventstream_stream_name = "ds-mcd-eyecue-eventstream"
}

locals {
  sql_template = "SELECT *, {store_id_expr} as store_id, '{event_type}' as event_type, '{client_name}' as client_name FROM '{topic}'"
}

###
### IoT Topic Rules Configuration
### roi --> eventstream
### hvi --> eventstream
### aggregate --> aggregate
### departure --> eventstream
### danger-zone () --> eventstream
### Do not change the order of the rules,add new rules at the end of the list
###
locals {
  kinesis_iot_topic_rules_config_old = {
    #TODO: Remove the old IOT routing rules once we have the new Kinesis streams up and running correctly
    "roi_eyecue_iot_kinesis_eventstream" = {
      name        = "roi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"
      sql         = replace(replace(replace(replace(local.sql_template, "{store_id_expr}", "regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)')"), "{event_type}", "roi"), "{client_name}", var.kinesis_client_name), "{topic}", "/eyeq/roievent/#")
      stream_name = local.eventstream_stream_name
      enabled     = true
    },
    "hvi_eyecue_iot_kinesis_eventstream" = {
      name        = "hvi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT HVI events to Kinesis Data Stream"
      sql         = replace(replace(replace(replace(local.sql_template, "{store_id_expr}", "regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)')"), "{event_type}", "hvi"), "{client_name}", var.kinesis_client_name), "{topic}", "/eyeq/hci/#")
      stream_name = local.eventstream_stream_name
      enabled     = true
    },
    "aggregated_eyecue_iot_kinesis_eventstream" = {
      name        = "aggregated_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Aggregated events to Kinesis Data Stream"
      sql         = replace(replace(replace(replace(local.sql_template, "{store_id_expr}", "site_id"), "{event_type}", "aggregate"), "{client_name}", var.kinesis_client_name), "{topic}", "/eyeq/vehicle-aggregated-2-0/#")
      stream_name = local.aggregate_stream_name
      enabled     = true
    },
    "departure_eyecue_iot_kinesis_eventstream" = {
      name        = "departure_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"
      sql         = replace(replace(replace(replace(local.sql_template, "{store_id_expr}", "site_id"), "{event_type}", "departure"), "{client_name}", var.kinesis_client_name), "{topic}", "/eyeq/departure/#")
      stream_name = local.eventstream_stream_name
      enabled     = true
    },
    "danger_zone_eyecue_iot_kinesis_eventstream" = {
      name        = "danger_zone_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Danger Zone events to Kinesis Data Stream"
      sql         = replace(replace(replace(replace(local.sql_template, "{store_id_expr}", "topic(4)"), "{event_type}", "danger-zone"), "{client_name}", var.kinesis_client_name), "{topic}", "/eyeq/danger-zones/+")
      stream_name = local.eventstream_stream_name
      enabled     = true
    }
  }
}

locals {
  kinesis_iot_topic_rules_config = {
    ###### New IOT configurations for new Kinesis streams with updated naming #####
    "roi_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_roi"
      description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'roi' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/roievent/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic HVI - use as Interaction in Data Environment
    "interaction_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_interaction"
      description = "Topic Rule for Forwarding IoT Interaction(HVI) events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'interaction' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/hci/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "hvi_looptimer_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_hvi_loop_timer"
      description = "Topic Rule for Forwarding IoT unregistered HVI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'hvi-looptimer' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/hci-looptimer/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic Aggregate - use as Journey in Data Environment
    "journey_eyecue_iot_kinesis_journey" = {
      name        = "kinesis_journey_stream_aggregate"
      description = "Topic Rule for Forwarding IoT Journey (Aggregated) events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'journey' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/vehicle-aggregated-2-0/#'"
      stream_name = local.journey_stream
      enabled     = true
    },
    "departure_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_departure"
      description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'departure' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/departure/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "arrival_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_arrival"
      description = "Topic Rule for Forwarding IoT Arrival events to Kinesis Data Stream"
      sql         = "SELECT *, arrival.site_id as store_id, 'arrival' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/arrival/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic Danger Zone use as Queue Zone in Data Environment
    "queue_zone_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_queue_zone"
      description = "Topic Rule for Forwarding IoT Queue Zone (Danger Zone) events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'queue-zone' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/danger-zones/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "lead_car_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_lead_car"
      description = "Topic Rule for Forwarding IoT Lead Car Timer events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'lead-car' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/roicount/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "active_departure_eyecue_iot_kinesis_drivethru_events" = {
      name        = "kinesis_drivethru_stream_active_departure"
      description = "Topic Rule for Forwarding IoT active Departure events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'active-departure-rate' as event_type, '${var.kinesis_client_name}' as client_name FROM '/eyeq/active-departure-rate/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    }
  }
}

variable "AWS_REGION" {
  default = "ap-southeast-2"
}

variable "CLIENT_NAME" {
  type    = string
  default = "mcd"
}
variable "ENVIRONMENT" {}


variable "CLIENT_ACRONYM" {
  type    = string
  default = "mcd"
}

variable "COUNTRY" {
  type    = string
  default = "au"
}

variable "COUNTRY_FULL" {
  type    = string
  default = "aus"
}

variable "KEYBASE" {
  type    = string
  default = "keybase:fingermark"
}

### NETWORK Module ###

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["ap-southeast-2a", "ap-southeast-2c", "ap-southeast-2b"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "prod"
  }
}

variable "customer" {
  default     = "mcd"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "sns_topics" {
  type = map(object({
    topic_name : string
    display_name : string
    tags : map(string)
    subscriptions : optional(object({
      protocol : string
      endpoint : string
    }), null)
  }))
  description = "List of SNS topics to create"
  default = {
    cloudwatch-alarm-alert-helper-topic : {
      topic_name   = "cloudwatch-alarm-alert-helper-topic"
      display_name = "Send Alert From SNS to eyecue-app-team-alerts Slack Channel"
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      subscriptions = {
        protocol = "email"
        endpoint = "<EMAIL>"
      }
    },
  }
}

variable "product" {
  default     = "eyecue"
  description = "Fingermark Product"
  type        = string
}

### kinesis data retention period set as 72 hours due to the data team data retention period requirement
variable "kinesis_data_stream_retention_period" {
  description = "value in hours"
  type        = number
  default     = 72
}
variable "kinesis_data_stream_stream_mode" {
  type    = string
  default = "ON_DEMAND"
}

variable "redshift_aws_account_ids_roles" {
  description = "List of Data-Redshift AWS Account IDs and Roles"
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_role_ap_southeast_2" }
  ]
}

variable "lambda_role_arn_suffixes" {
  description = "List of Lambda role ARN suffixes"
  type        = list(string)
  default = [
    "eyecue-mimir-blue-prod-ap-southeast-2-lambdaRole",
    "project-mimir-prod-prod-ap-southeast-2-lambdaRole",
    "eyecue-mimir-green-prod-ap-southeast-2-lambdaRole"
  ]
}

variable "kinesis_client_name" {
  description = "Kinisis client name due to the CLIENT_NAME and CLIENT_ACRONYM not defined as data team is not using the standard naming convention"
  type        = string
  default     = "mcd-aus"

}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}

variable "dynamodb_cw_alarms_defaults_tables_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB tables in ap-southeast-2 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in ap-southeast-2 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}
