
AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "cvland-dev"
CLIENT_ACRONYM = "cvland-dev"
COUNTRY        = "au"
ENVIRONMENT    = "dev"
# RDS_MASTER_INSTANCE_CLASS = "db.t3.micro"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "argus-connections"                                  = { table_name = "argus-connections" },
  "argus-messages"                                     = { table_name = "argus-messages" },
  "auto_provisioner_api_logs"                          = { table_name = "auto_provisioner_api_logs" },
  "custom-dayparts-and-shifts"                         = { table_name = "custom-dayparts-and-shifts" },
  "databoard-report-bull-jobs"                         = { table_name = "databoard-report-bull-jobs" },
  "databoard-report-users"                             = { table_name = "databoard-report-users" },
  "databoard-site-configs"                             = { table_name = "databoard-site-configs" },
  "databoard-site-configs-qa"                          = { table_name = "databoard-site-configs-qa" },
  "downtime-monitoring"                                = { table_name = "downtime-monitoring" },
  "eyecue-dashboard-cameras"                           = { table_name = "eyecue-dashboard-cameras" },
  "eyecue-dashboard-clients"                           = { table_name = "eyecue-dashboard-clients" },
  "EyecueDashboardDashboardsTable"                     = { table_name = "EyecueDashboardDashboardsTable" },
  "eyecue-data-capture"                                = { table_name = "eyecue-data-capture" },
  "eyecue-deployer-params"                             = { table_name = "eyecue-deployer-params" },
  "eyecue-deployment-params-template"                  = { table_name = "eyecue-deployment-params-template" },
  "eyecue-inventory-table"                             = { table_name = "eyecue-inventory-table" },
  "eyecue-machine-containers"                          = { table_name = "eyecue-machine-containers" },
  "eyecue-provisioning-log"                            = { table_name = "eyecue-provisioning-log" },
  "eyecue-provisioning-table"                          = { table_name = "eyecue-provisioning-table" },
  "eyecue-roi-sugestor-trajectories-clusters-table"    = { table_name = "eyecue-roi-sugestor-trajectories-clusters-table" },
  "eyecue-scheduled-validation"                        = { table_name = "eyecue-scheduled-validation" },
  "eyecue-sites-configuration"                         = { table_name = "eyecue-sites-configuration" },
  "eyecue-sites-container"                             = { table_name = "eyecue-sites-container" },
  "eyecue-store-central-accounts-dev"                  = { table_name = "eyecue-store-central-accounts-dev" },
  "eyecue-store-central-servers-dev"                   = { table_name = "eyecue-store-central-servers-dev" },
  "eyecue-things-shadow"                               = { table_name = "eyecue-things-shadow" },
  "eyecue-trigger-execution-history"                   = { table_name = "eyecue-trigger-execution-history" },
  "eyecue-trigger-service"                             = { table_name = "eyecue-trigger-service" },
  "eyecue-tunnel-table"                                = { table_name = "eyecue-tunnel-table" },
  "eyecue-validation-queue"                            = { table_name = "eyecue-validation-queue" },
  "eyecue-validation-results"                          = { table_name = "eyecue-validation-results" },
  "eyecue-weights"                                     = { table_name = "eyecue-weights" },
  "eyecue-weights-template"                            = { table_name = "eyecue-weights-template" },
  "helm-charts"                                        = { table_name = "helm-charts" },
  "m2m-token-cache-dev"                                = { table_name = "m2m-token-cache-dev" },
  "northvue-capture-api-dev"                           = { table_name = "northvue-capture-api-dev" },
  "np6-data"                                           = { table_name = "np6-data" },
  "poc-websockets"                                     = { table_name = "poc-websockets" },
  "ratatoskr-chromebox-configuration"                  = { table_name = "ratatoskr-chromebox-configuration" },
  "ratatoskr-dashboard-client-specific-configurations" = { table_name = "ratatoskr-dashboard-client-specific-configurations" },
  "ratatoskr-dashboard-configuration"                  = { table_name = "ratatoskr-dashboard-configuration" },
  "Site"                                               = { table_name = "Site" },
  "sso-tokens"                                         = { table_name = "sso-tokens" },
  "staging-databoard-report-users"                     = { table_name = "staging-databoard-report-users" },
  "staging-databoard-site-configs"                     = { table_name = "staging-databoard-site-configs" },
  "eyecue-dashboard-stores-restored"                   = { table_name = "eyecue-dashboard-stores-restored" },
  "eyecue-report-service-report-templates"             = { table_name = "eyecue-report-service-report-templates" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {
  "eyecue-trigger-execution-history-triggerIdIndex"      = { table_name = "eyecue-trigger-execution-history", index_name = "triggerIdIndex" },
  "ratatoskr-dashboard-configuration-dontShowHats-index" = { table_name = "ratatoskr-dashboard-configuration", index_name = "dontShowHats-index" },
  "Site-GSIDeploymentDate"                               = { table_name = "Site", index_name = "GSIDeploymentDate" },
  "Site-GSIFingermarkID"                                 = { table_name = "Site", index_name = "GSIFingermarkID" },
}
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "eyecue-provisioning-log"   = { table_name = "eyecue-provisioning-log" },
  "eyecue-provisioning-table" = { table_name = "eyecue-provisioning-table" },
  "eyecue-tunnel-table"       = { table_name = "eyecue-tunnel-table" },
  "serverless-graphql-dev"    = { table_name = "serverless-graphql-dev" },
}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {}
