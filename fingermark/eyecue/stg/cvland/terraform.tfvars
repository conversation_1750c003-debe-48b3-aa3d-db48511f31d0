AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "cvland-stg"
CLIENT_ACRONYM = "cvland-stg"
COUNTRY        = "au"
ENVIRONMENT    = "stg"
# RDS_MASTER_INSTANCE_CLASS = "db.t3.micro"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "databoard-report-users-qa"                          = { table_name = "databoard-report-users-qa" },
  "databoard-site-configs"                             = { table_name = "databoard-site-configs" },
  "eyecue-deployment-params-template"                  = { table_name = "eyecue-deployment-params-template" },
  "eyecue-trigger-execution-history"                   = { table_name = "eyecue-trigger-execution-history" },
  "eyecue-trigger-service"                             = { table_name = "eyecue-trigger-service" },
  "m2m-token-cache-staging"                            = { table_name = "m2m-token-cache-staging" },
  "ratatoskr-dashboard-client-specific-configurations" = { table_name = "ratatoskr-dashboard-client-specific-configurations" },
  "ratatoskr-dashboard-configuration"                  = { table_name = "ratatoskr-dashboard-configuration" },
  "eyecue-dashboard-clients"                           = { table_name = "eyecue-dashboard-clients" },
  "eyecue-dashboard-cameras"                           = { table_name = "eyecue-dashboard-cameras" },
  "eyecue-store-central-servers-stg"                   = { table_name = "eyecue-store-central-servers-stg" },
  "eyecue-store-central-accounts-stg"                  = { table_name = "eyecue-store-central-accounts-stg" },
  "eyecue-report-service-report-templates"             = { table_name = "eyecue-report-service-report-templates" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {
  "eyecue-trigger-execution-history-triggerIdIndex" = { table_name = "eyecue-trigger-execution-history", index_name = "triggerIdIndex" },
}
