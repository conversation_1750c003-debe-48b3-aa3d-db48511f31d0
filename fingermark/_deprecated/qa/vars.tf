###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

variable "AWS_REGION" {
  default = "ap-southeast-2"
}

variable "ENVIRONMENT" {}

variable "CLIENT_NAME" {
  type    = string
  default = "qa"
}

variable "CLIENT_ACRONYM" {
  type    = string
  default = "qa"
}

variable "COUNTRY" {
  type    = string
  default = "au"
}

variable "COUNTRY_FULL" {
  type    = string
  default = "aus"
}

variable "KEYBASE" {
  type    = string
  default = "keybase:fingermark"
}

variable "sns_topics" {
  type = map(object({
    topic_name : string
    display_name : string
    tags : map(string)
    subscriptions : optional(object({
      protocol : string
      endpoint : string
    }), null)
  }))
  description = "List of SNS topics to create"
  default = {
    cloudwatch-alarm-alert-helper-topic : {
      topic_name   = "cloudwatch-alarm-alert-helper-topic"
      display_name = "Send Alert From SNS to eyecue-app-team-alerts Slack Channel"
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      subscriptions = {
        protocol = "email"
        endpoint = "<EMAIL>"
      }
    },
  }
}

### RDS Module ###
variable "RDS_MASTER_INSTANCE_CLASS" {
  default = "db.t3.micro"
}

### NETWORK Module ###

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["ap-southeast-2c", "ap-southeast-2b", "ap-southeast-2a"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "qa"
  }
}

variable "customer" {
  default     = "fingermark"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "qa"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "eyecue"
  description = "Fingermark Product"
  type        = string
}
variable "lambda_role_arn_suffixes" {
  description = "List of Lambda role ARN suffixes"
  type        = list(string)
  default = [
    "eyecue-mimir-blue-prod-ap-southeast-2-lambdaRole",
    "project-mimir-prod-prod-ap-southeast-2-lambdaRole",
    "eyecue-mimir-green-prod-ap-southeast-2-lambdaRole"
  ]
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}

variable "dynamodb_cw_alarms_defaults_tables_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB tables in ap-southeast-2 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in ap-southeast-2 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_tables_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB tables in us-east-1 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in us-east-1 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}
