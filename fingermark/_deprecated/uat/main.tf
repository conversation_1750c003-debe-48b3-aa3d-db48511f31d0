module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

module "common" {
  source            = "../../../modules/common"
  aws_account_id    = data.aws_caller_identity.current.account_id
  aws_region        = var.AWS_REGION
  client_name       = var.CLIENT_NAME
  client_acronym    = var.CLIENT_ACRONYM
  country           = var.COUNTRY
  country_full      = var.COUNTRY_FULL
  aws_iam_roles     = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DbaAccess"]
  keybase           = var.KEYBASE
  cloudcraft_access = true
  env               = var.ENVIRONMENT
}

module "eyecue_notification_service" {
  source         = "../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

module "eyecue_network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "secret_manager" {
  source                              = "../../../modules/secret_manager"
  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"
  eyecue_dashboard_data_secret_name   = "rds/ssm/eyecue-dashboard-data"
}

module "icinga2_satellite" {
  source                                         = "../../../modules/icinga2_satellite"
  icinga2_satellite_vpc_id                       = module.eyecue_network.vpc_id
  icinga2_satellite_ec2_ami_id                   = "ami-0df609f69029c9bdb"
  icinga2_satellite_ec2_subnet_id                = module.eyecue_network.public_subnet_ids[1]
  icinga2_satellite_ec2_extra_security_group_ids = [module.eyecue_network.havelock_security_group_id]
  icinga2_satellite_ec2_ssh_key_name             = "infra-team"
  icinga2_satellite_customer_id                  = var.CLIENT_ACRONYM
  icinga2_satellite_cloudflare_api_key           = data.vault_generic_secret.cloudflare.data["api_key"]
}

module "eyecue_customer_edw_integration" {
  source                = "../../../modules/eyecue_customer_edw_integration"
  client_name           = var.CLIENT_NAME
  country               = var.COUNTRY
  bucket_name_reference = "edw-integration"
  keybase               = var.KEYBASE
}

module "aws_config_recorder" {
  source = "../../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags         = var.tags
  default_tags = var.default_tags
}

# ==========================================
# SOC2 Security
# ==========================================

module "vanta" {
  source = "../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source        = "../../../modules/sqs_monitoring"
  sns_topic_arn = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_us_east_1" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "icinga2-satellite" = { instance_tags = { Name = "icinga2-satellite" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
