variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Account Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Account Default Tags"
  type        = map(any)
  default = {
    Terraform   = "True"
    Product     = "Northvue"
    Environment = "Prod"
  }
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "northvue"
  description = "Fingermark Product"
  type        = string
}


### kinesis data stream Module ###
variable "redshift_aws_account_id" {
  type    = string
  default = "************"
}
variable "retention_period" {
  type    = number
  default = 24
}
variable "stream_mode" {
  type    = string
  default = "ON_DEMAND"
}

variable "redshift_aws_account_ids_roles" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_role_us_east_1" }
  ]
}


variable "stream_name_list" {
  description = "List of stream types"
  type        = list(string)
  default     = ["eventstream"]
}


variable "CLIENT_NAME" {
  type    = string
  default = "northvue"
}

variable "AWS_REGION" {
  default = "us-east-1"
}

variable "kinesis_iot_topic_rules_config" {
  description = "Configuration for each IoT topic rule"
  type = map(object({
    name : string
    sql : string
    stream_name : string
    description : string
    enabled : bool
  }))
  default = {
    "table_status_eyecue_iot_kinesis_eventstream" = {
      name        = "table_status_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Table Status events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/table-status/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
    "workstation_usage_eyecue_iot_kinesis_eventstream" = {
      name        = "workstation_usage_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Workstation Usage events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/workstation-usage/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
    "chip_bay_state_eyecue_iot_kinesis_eventstream" = {
      name        = "chip_bay_state_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Chip Bay State events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/chip-bay-state/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
    "customer_in_line_eyecue_iot_kinesis_eventstream" = {
      name        = "customer_in_line_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Customer In Line events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/customer-in-line/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
    "customer_entrance_eyecue_iot_kinesis_eventstream" = {
      name        = "customer_entrance_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Customer Entrance events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/customer-entrance/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
    "items_wait_eyecue_iot_kinesis_eventstream" = {
      name        = "items_wait_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Items Wait events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/items-wait/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
    "inventory_tracking_eyecue_iot_kinesis_eventstream" = {
      name        = "inventory_tracking_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Inventory Tracking events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'northvue-usa' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/inventory-tracking/+'"
      stream_name = "ds-northvue-eyecue-eventstream"
      enabled     = true
    },
  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "dynamodb_cw_alarms_defaults_tables_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB tables in us-east-1 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_us_east_1" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in us-east-1 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}

variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
