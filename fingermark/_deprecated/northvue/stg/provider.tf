terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "< 6.0.0" # pinning to avoid breaking changes in AWS v6+
                          # and since this environment is deprecated 
                          # will likely not be updated to v6+.
    }
  }
}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::026541673306:role/AdminAccess"
    session_name = "NorthVue-STG"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
